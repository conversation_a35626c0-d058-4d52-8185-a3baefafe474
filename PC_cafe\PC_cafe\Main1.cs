﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace PC_cafe
{
    public partial class Main1 : Form
    {

        private readonly UserInfo _userInfo;

        public Main1(UserInfo userInfo)
        {
            InitializeComponent();
            StartPosition = FormStartPosition.CenterScreen;
            _userInfo = userInfo;
        }


        private void main1_FormClosed(object sender, FormClosedEventArgs e)
        {
            LogForm logForm = new LogForm();
            logForm.Show();
        }

        private void pictureBoxLow_MouseEnter(object sender, EventArgs e)
        {
           
           // pictureBoxLow.Image?.Dispose();
            pictureBoxLow.Image = Image.FromFile("C:\\Users\\<USER>\\source\\repos\\PC_cafe\\PC_cafe\\Resources\\gLowCostZal.png");
        }

        private void pictureBoxLow_MouseLeave(object sender, EventArgs e)
        {
           // pictureBoxLow.Image?.Dispose();
            pictureBoxLow.Image = Image.FromFile("C:\\Users\\<USER>\\source\\repos\\PC_cafe\\PC_cafe\\Resources\\lowCostZal.png");
        }

        private void pictureBoxBase_MouseEnter(object sender, EventArgs e)
        {
            pictureBoxBase.Image = Image.FromFile("C:\\Users\\<USER>\\source\\repos\\PC_cafe\\PC_cafe\\Resources\\gBaseZal.png");
        }

        private void pictureBoxBase_MouseLeave(object sender, EventArgs e)
        {
           // pictureBoxBase.Image?.Dispose();
            pictureBoxBase.Image = Image.FromFile("C:\\Users\\<USER>\\source\\repos\\PC_cafe\\PC_cafe\\Resources\\baseZal.png");
        }

        private void pictureBoxPremium_MouseEnter(object sender, EventArgs e)
        {
           // pictureBoxPremium.Image?.Dispose();
            pictureBoxPremium.Image = Image.FromFile("C:\\Users\\<USER>\\source\\repos\\PC_cafe\\PC_cafe\\Resources\\gPremiumZal.png");
        }

        private void pictureBoxPremium_MouseLeave(object sender, EventArgs e)
        {
           // pictureBoxPremium.Image?.Dispose();
            pictureBoxPremium.Image = Image.FromFile("C:\\Users\\<USER>\\source\\repos\\PC_cafe\\PC_cafe\\Resources\\PremiumZal.png");
        }

        private void pictureBoxLow_Click(object sender, EventArgs e)
        {
            LowForm lowForm = new LowForm(_userInfo);
            lowForm.Show();
            this.Hide();
        }
        /*
        private string ReadSingleRow(IDataRecord record)
        {
            string check;
            try
            {
                check = record.GetString(3);
            }

            catch
            {
                check = "-";
            }
            string info = $"{record.GetString(4)} {record.GetString(5)} {record.GetString(1)} {record.GetString(2)} {check}";
            return info;
        }
        */
        private void pictureBoxUserMain1_Click(object sender, EventArgs e)
        {
            /*
            string info = "";

            SqlCommand command = new SqlCommand($"SELECT * FROM Пользователь WHERE ID_пользователя = @userID", dataBase.GetConnection());
            command.Parameters.Add("@userID", SqlDbType.Int).Value = _userInfo._userID;

            dataBase.OpenConnection();

            SqlDataReader reader = command.ExecuteReader();

            while(reader.Read())
            {
                info = ReadSingleRow(reader);
            }
            reader.Close();
            string[] inform = info.Split(' ');
            string LOGIN = inform[0];
            string PASSWORD = inform[1];
            string F = inform[2];
            string I = inform[3];
            string O = inform[4];
            */
            ContextMenuStrip contextMenu = new ContextMenuStrip();
            ToolStripMenuItem menuItem1 = new ToolStripMenuItem("Логин и ФИО");
            contextMenu.Items.Add(menuItem1);
            ToolStripMenuItem subMenuItem1_1 = new ToolStripMenuItem(_userInfo._userLogin);

            ToolStripMenuItem subMenuItem1_2 = new ToolStripMenuItem($"{_userInfo._userSname}\n{_userInfo._userFname}\n{_userInfo._userTname}");
            menuItem1.DropDownItems.Add(subMenuItem1_1);
            menuItem1.DropDownItems.Add(subMenuItem1_2);
            /*
            ToolStripMenuItem menuItem2 = new ToolStripMenuItem("История (В разработке)"); 
            menuItem2.Click += MenuItem2_Click; 
            contextMenu.Items.Add(menuItem2);

            ToolStripMenuItem menuItem3 = new ToolStripMenuItem("Сменить логин и ФИО (В разработке)");
            menuItem3.Click += MenuItem3_Click; 
            contextMenu.Items.Add(menuItem3);

            ToolStripMenuItem menuItem4 = new ToolStripMenuItem("Сменить пароль (В разработке)");
            menuItem4.Click += MenuItem4_Click;
            contextMenu.Items.Add(menuItem4);
            */
            pictureBoxUserMain1.ContextMenuStrip = contextMenu; 
        }

        private void MenuItem2_Click(object sender, EventArgs e)
        {
            // Придумать реализацию истории
        }
        private void MenuItem3_Click(object sender, EventArgs e)
        {
            // Смена логина
        }
        private void MenuItem4_Click(object sender, EventArgs e)
        {
            // Смена ФИО
        }

        private void pictureBoxUserMain1_MouseHover(object sender, EventArgs e)
        {
            toolTipForUserMain1.SetToolTip(pictureBoxUserMain1, "Нажмите правую кнопку мыши для получения подробностей");
        }

        private void pictureBoxUserMain1_MouseLeave(object sender, EventArgs e)
        {
            toolTipForUserMain1.Hide(pictureBoxUserMain1);
        }

        private void pictureBoxBase_Click(object sender, EventArgs e)
        {
            BaseForm lowForm = new BaseForm(_userInfo);
            lowForm.Show();
            this.Hide();
        }

        private void pictureBoxPremium_Click(object sender, EventArgs e)
        {
            PremForm premForm = new PremForm(_userInfo);
            premForm.Show();
            this.Hide();
        }
    }
}
