﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{72BED120-E8C7-4AE1-B061-9DC68AB49678}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>PC_cafe</RootNamespace>
    <AssemblyName>PC_cafe</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BaseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BaseForm.Designer.cs">
      <DependentUpon>BaseForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Md5.cs" />
    <Compile Include="PremForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PremForm.Designer.cs">
      <DependentUpon>PremForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Rent.cs" />
    <Compile Include="PCInfo.cs" />
    <Compile Include="DataBase.cs" />
    <Compile Include="LowForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LowForm.Designer.cs">
      <DependentUpon>LowForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Main1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Main1.Designer.cs">
      <DependentUpon>Main1.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="RegForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RegForm.Designer.cs">
      <DependentUpon>RegForm.cs</DependentUpon>
    </Compile>
    <Compile Include="LogForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LogForm.Designer.cs">
      <DependentUpon>LogForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="UserInfo.cs" />
    <Compile Include="_PC_cafeDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>_PC_cafeDataSet.xsd</DependentUpon>
    </Compile>
    <EmbeddedResource Include="BaseForm.resx">
      <DependentUpon>BaseForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LowForm.resx">
      <DependentUpon>LowForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Main1.resx">
      <DependentUpon>Main1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LogForm.resx">
      <DependentUpon>LogForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PremForm.resx">
      <DependentUpon>PremForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="RegForm.resx">
      <DependentUpon>RegForm.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="Resources\LowPC2.png" />
    <None Include="_PC_cafeDataSet.xsc">
      <DependentUpon>_PC_cafeDataSet.xsd</DependentUpon>
    </None>
    <None Include="_PC_cafeDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>_PC_cafeDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <None Include="_PC_cafeDataSet.xss">
      <DependentUpon>_PC_cafeDataSet.xsd</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\2022-07-13-rgb-lines-1-51314.jpeg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\userImg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lockImg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\newUserImg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\openlockImg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\lowCostZal.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\baseZal.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\PremiumZal.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\LowPC1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\userForMain1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\BasePC1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\BasePC2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\PremiumPC1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\PremiumPC2.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>