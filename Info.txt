 
Министерство науки и высшего образования Российской Федерации  
Федеральное государственное автономное образовательное учреждение 
высшего образования  
ТОМСКИЙ ГОСУДАРСТВЕННЫЙ УНИВЕРСИТЕТ СИСТЕМ  
УПРАВЛЕНИЯ И РАДИОЭЛЕКТРОНИКИ (ТУСУР)  
Кафедра комплексной информационной безопасности электронно
вычислительных систем (КИБЭВС)  
  
   
АВТОМАТИЗИРОВАННАЯ ИНФОРМАЦИОННАЯ СИСТЕМА ДЛЯ 
КОМПЬЮТЕРНОГО КЛУБА  
 
Отчет по практической работе №2   
По дисциплине «Проектирование защищенных телекоммуникационных  
систем»  
  
 
 
   Исполнитель  
    Студент гр. 721-1  
                                                                                               В.А. Власов          
                          ____________ 
     
                Принял  
                                                                     м.н.с каф. КИБЭВС  
                                                             _____________ И.В. Ковалев 
                                _____________ 
 
 
 
Томск 2025  
Перечень сокращений  
ЗБ – Задание по безопасности 
ОО – Объект оценки 
GUI - Графический интерфейс пользователя  
.NET - Платформа Microsoft .NET Framework  
C# - Язык программирования C#  
Windows - Операционная система Microsoft Windows  
СУБД - Система управления базами данных 
АИС - Автоматизированная информационная система 
2  
1 ВВЕДЕНИЕ ЗБ  
Данный раздел содержит информацию общего характера. Подраздел 
«Идентификация ЗБ» предоставляет маркировку и описательную информацию, 
которые необходимы, чтобы идентифицировать, каталогизировать ЗБ и 
ссылаться на него. Подраздел «Аннотация ЗБ» содержит общую характеристику 
ЗБ, позволяющую определить применимость настоящего ЗБ в конкретной 
ситуации. В подразделе «Соглашения» дается описание операций 
конкретизации компонентов требований безопасности ИТ. В подразделе 
«Термины» представлены определения основных терминов, специфичных для 
данного ЗБ. В подразделе «Организация ЗБ» дается пояснение организации 
документа.   
1.1 Идентификация ЗБ  
Название ЗБ: 
Автоматизированная информационная система для 
компьютерного клуба. Задание по безопасности. 
Версия ЗБ: 
Обозначение: 
Версия 1.0. 
АИС для компьютерного клуба. ЗБ. 
Идентификации ОО: 
Автоматизированная информационная система для 
компьютерного клуба. 
Уровень доверия: 
Идентификация ОК: 
ОУД 1. 
ГОСТ Р ИСО/МЭК 15408–2002 Информационная 
технология. 
Методы и средства Критерии 
обеспечения оценки безопасности. безопасности 
информационных технологий. Части 1, 2, 3; 
Руководящий Безопасность информационных 
3  
4  
оценки безопасности документ. 
технологий.Критерии информационных 
технологий. Часть 1: Введение и общая модель, 
Гостехкомиссия России, 2002; Руководящий 
документ. информационных технологий. Критерии 
оценки Безопасность безопасности 
информационных технологий. Часть 2: 
Функциональные требования безопасности, 
Гостехкомиссия России, 2002; Руководящий 
информационных оценки документ. Безопасность 
технологий.Критерии безопасности 
информационных технологий. Часть 3: Требования 
доверия к безопасности, Гостехкомиссия России, 
2002; Common Criteria for Information Technology 
Security Evaluation, Version 2.1, August 1999. 
ISO/IEC 15408:1999; 
Ключевые слова: Безопасность, конфиденциальность, защита, 
задание по безопасности, АИС, авторизация, СУБД. 
 
1.2 Аннотация ЗБ  
  
Настоящее ЗБ определяет требования безопасности к объекту оценки, 
АИС для компьютерного клуба, предназначенной для:  
• Авторизации и регистрации пользователей;  
• Аренда компьютерного места;  
• Ведение журнала аренды.  
Объект оценки представляет собой программное обеспечение, 
реализованное в виде клиентского приложения, взаимодействующего с 
внешней базой данных. Приложение не имеет постоянного выхода в интернет, 
сетевое взаимодействие осуществляется только при обращении к БД.  
1.3 Соответствие ОК  
Объект оценки и настоящее ЗБ согласованы со следующими 
нормативными документами:  
• Руководящий документ. Руководство по разработке профилей 
защиты и заданий по безопасности, Гостехкомиссия России, 2003 год.  
• Руководящий  документ.  Безопасность  
информационных 
технологий. Критерии оценки безопасности информационных технологий. 
Часть 2: Функциональные требования безопасности, Гостехкомиссия России, 
2002 год (соответствие части 2 ОК).  
• Руководящий документ. Безопасность информационных 
технологий. Критерии оценки безопасности информационных технологий. 
Часть 3: Требования доверия к безопасности, Гостехкомиссия России, 2002 год 
(усиление части 3 ОК, уровень ОУД 1).   
1.4 Термины и определения  
В настоящем ЗБ применяются следующие термины с соответствующими 
определениями.  
Аутентификационные данные – информация, используемая для 
верификации предъявленного идентификатора.   
Аутентификация – процесс установления подлинности информации,  
Достоверность – 
предусмотренным значениям.  
свойство, 
обеспечивающее 
соответствие 
Задание по безопасности – совокупность требований безопасности и 
спецификаций, предназначенная для использования в качестве основы для 
оценки конкретного ОО.  
Идентификатор – уникальный признак уполномоченного субъекта, 
однозначно его идентифицирующий.  
5  
Конфиденциальность – свойство предотвращать возможность доступа к 
информации и/или ее раскрытия неуполномоченным лицам, объектам или 
процессам.  
Объект – сущность в пределах ОДФ, которая содержит или получает 
информацию, и над которой субъекты выполняют операции.  
Объект оценки – подлежащие оценке продукт ИТ или система ИТ с 
руководствами администратора или пользователя.  
Пользователь – любая сущность (человек-пользователь или внешняя 
сущность ИТ) вне ОО, которая взаимодействует с ОО.  
Администратор ОО – группа уполномоченных пользователей, 
ответственных за установку, администрирование и эксплуатацию ОО.  
Профиль защиты – независимая от реализации совокупность требований 
безопасности для некоторой категории ОО, отвечающая специфическим 
запросам.  
Субъект – сущность в пределах ОДФ, которая инициирует выполнение 
операций.  
Функции безопасности ОО – совокупность всех функций безопасности 
ОО, направленных на осуществление ПБО.  
Функция безопасности – функциональные возможности части или частей 
ОО, обеспечивающие выполнение подмножества взаимосвязанных правил 
ПБО.  
Целостность – свойство поддержания полноты и неизменности 
информации.  
1.5 Организация ЗБ  
Раздел 
1 «Введение ЗБ» содержит информацию управления 
документооборотом и описательную информацию, необходимые для 
идентификации ЗБ и ОО, к которому оно относится.   
6  
Раздел 2 «Описание ОО» содержит описание функциональных 
возможностей среды функционирования и границ ОО, служащее цели лучшего 
понимания его требований безопасности и дающее представление о типе 
продукта.   
Раздел 3 «Среда безопасности ОО» содержит описание аспектов среды 
безопасности ОО. В данном разделе определяется совокупность угроз, 
имеющих отношение к безопасному функционированию ОО, политика 
безопасности организации, которой должен следовать ОО, и предположения 
(обязательные условия) безопасного использования ОО.   
В разделе 4 «Цели безопасности» определена совокупность целей 
безопасности для ОО и среды функционирования ОО.   
В разделе 5 «Требования безопасности ИТ» на основе частей 2 и 3 ОК 
определены, соответственно, функциональные требования безопасности ИТ и 
требование доверия к безопасности.  
В раздел 6 «Краткая спецификация ОО» включено описание функций 
безопасности ИТ, реализуемых ОО и соответствующих специфицированным 
функциональным требованиям безопасности, а также мер доверия к 
безопасности, соответствующих специфицированным требованиям доверия к 
безопасности.   
В разделе 7 «Утверждения о соответствии ПЗ» идентифицируется ПЗ, о 
соответствии которому заявляется в ЗБ, а также дополнения и уточнения целей 
и требований.   
В Разделе 8 «Обоснование» демонстрируется, что ЗБ специфицирует 
полную и взаимосвязанную совокупность требований безопасности ИТ, что ОО 
учитывает идентифицированные аспекты среды безопасности ИТ, а также что 
функции безопасности ИТ и меры доверия к безопасности соответствуют 
требованиям безопасности ОО.  
7  
2 ОПИСАНИЕ ОО  
Система АИС ПК-кафе (далее Система) представляет собой программное 
обеспечение, предназначенное для автоматизации основных процессов 
компьютерного клуба. Система обеспечивает автоматизацию аренды 
компьютерного места, учета посетителей компьютерного клуба, управления 
сеансами пользователей, а также учета информации о компьютерных 
конфигурациях и залах, в которых размещены компьютеры. 
Система должна выполнять следующие функции:  - бронирование компьютерных мест в залах клуба;  - учет посетителей клуба;  - управление сеансами посетителей; - учет информации о пользователях и компьютерах;  
В Системе выделяются следующие функциональные подсистемы:  - подсистема бронирования мест;  - подсистема учета посетителей;  - подсистема управления сеансов посетителей;  - подсистема учета компьютеров; 
Для 
эксплуатации 
разрабатываемой информационной системы 
необходимы следующие условия:  - Компьютер под управлением операционной системы Windows 10 
или Windows 11; - Наличие таких периферийных устройств, как мышь и клавиатура, 
для взаимодействия;  - СУБД Microsoft SQL Server; Предустановленный .Net Framework v 
4.7.2 или выше. 
8  
3 СРЕДА БЕЗОПАСНОСТИ ОО 
Этот раздел описывает следующие аспекты безопасной среды объекта 
оценки (ОО):  
• предположения относительно использования ОО и условий 
безопасности окружающей среды;  
• заранее определённые угрозы, с которыми ОО должен уметь 
противостоять средствами обеспечения безопасности;  
• политику  безопасности  
требуется от объекта оценки.  
организации,  
3.1 Предположения безопасности  
соблюдение которой 
3.1.1 Предположения безопасности относительно предопределённого  
использования ОО  
A.Authorization_use: Доступ к ОО должен осуществляться только 
санкционированными пользователями. 
A.Local_Only: Все остальные модули приложения, кроме обращения к БД, 
не имеют выхода в сеть.  
A.Secure_Storage: Пароли должны храниться в БД в зашифрованном виде 
с использованием современных алгоритмов хэширования (например, bcrypt, 
Argon2).  
A.Secure_Updates: Устанавливать обновления ПО следует исключительно 
из проверенных и надежных источников, чтобы исключить попадание в систему 
вредоносных программ. Рекомендуется использовать официальные ресурсы 
разработчика ОО или его дистрибутива. 
9  
3.1.2 Предположения относительно среды функционирования ОО. 
Предположения, связанные с физической защитой ОО  
Не применяются. 
В 
данном 
пункте 
3.2 Угрозы  
будут 
рассмотрены угрозы, относящиеся 
непосредственно к объекту оценки, и угрозы, направленные на среду 
взаимодействия между элементами системы. В настоящем ЗБ определены 
угрозы для систем, которым необходимо противостоять средствами ОО.  
3.2.1 Актуальные угрозы  
УБИ. 8  
1. 
2. 
3. 
Аннотация угрозы – Подбор учетных данных методом перебора.  
Источники угрозы – Внешний нарушитель с низким потенциалом.  
Способ реализации угрозы – Многократные несанкционированные 
попытки авторизации с подобранными логинами/паролями.  
4. 
Используемые уязвимости – Простые или стандартные пароли, 
отсутствие блокировки аккаунта после нескольких неудачных попыток.  
5. 
Вид активов, потенциально подверженных угрозе – Сетевой узел, 
сетевое программное обеспечение, база данных.  
6. 
Нарушаемое 
свойство 
Конфиденциальность, доступность.  
7. 
безопасности 
активов – 
Возможные последствия реализации угрозы – Получение 
несанкционированного доступа к данным пользователя, утечка информации.  
10  
УБИ. 90 
1. 
Аннотация угрозы – Несанкционированное создание учётной записи 
пользователя.  
2. 
Источники угрозы – Внешний и внутренний нарушители с низким 
потенциалом.  
3. 
Способ реализации угрозы – Возможность создания нарушителем в 
системе дополнительной учётной записи пользователя и её дальнейшего 
использования в собственных целях.  
4. 
Используемые уязвимости – Слабость механизмов регистрации и 
контроля доступа.  
5. 
Вид активов, потенциально подверженных угрозе – База данных, 
пользовательские учетные записи.  
6. 
Нарушаемое свойство безопасности активов – Целостность, 
доступность.  
7. 
Возможные  
последствия  
реализации  
угрозы  
получение несанкционированного доступа, выполнение действий от имени 
легального пользователя.  
УБИ. 6 
1. Аннотация угрозы – Внедрение вредоносного кода или данных в 
систему с целью получения контроля над ресурсами или выполнения 
несанкционированных действий.  
2. Источники угрозы – Внешний нарушитель с низким потенциалом.  
3. 
Способ реализации угрозы – Запуск вредоносного кода через 
недоверенные файлы, уязвимости ПО, заводские учетные данные.  
4. Используемые уязвимости – Уязвимости ПО, слабая антивирусная 
защита, открытые порты (например, Telnet).  
11  
5. Вид активов, потенциально подверженных угрозе – Системное ПО, 
прикладное ПО, сетевое ПО.  
6. 
Нарушаемое свойство безопасности активов – Целостность, 
доступность.    
7. Возможные последствия реализации угрозы – Получение контроля над 
системой, повреждение данных, отказ в обслуживании. 
УБИ. 152, 74, 86  
1. Аннотация угрозы – Удаление, доступ, изменение аутентификационной 
информации пользователя с целью лишить его доступа к системе или получить 
несанкционированный доступ от его имени.  
2. Источники угрозы – Внешний нарушитель с низким потенциалом; 
Внутренний нарушитель с низким потенциалом.  
3. Способ реализации угрозы – Сброс, обнуление или удаление паролей и 
учётных данных пользователей через штатные средства системы или 19 
специальное ПО.  
4. Используемые уязвимости – Слабости политики разграничения доступа 
к аутентификационной информации и средствам работы с учётными записями.  
5. 
Вид активов, потенциально подверженных угрозе – Системное 
программное обеспечение, микропрограммное обеспечение, учётные данные 
пользователя.  
6. 
Нарушаемое свойство безопасности активов – Доступность, 
конфиденциальность.  
7. Возможные последствия реализации угрозы – Отказ в доступе для 
легитимного пользователя, получение нарушителем привилегий пользователя. 
УБИ. 115 
1. 
2. 
Аннотация угрозы – Загрузка вредоносных файлов в систему.  
Источники угрозы – Внешний нарушитель с низким потенциалом.  
12  
3. 
Способ реализации угрозы – Загрузка исполняемых файлов, 
содержащих вредоносный код, через интерфейсы загрузки.  
4. 
Используемые уязвимости – Слабая проверка типов файлов, 
возможность исполнения загруженных файлов.  
5. 
Вид активов, потенциально подверженных угрозе – Сервер, 
файловая система, пользовательские данные.  
6. 
Нарушаемое свойство безопасности активов – Целостность, 
доступность.  
7. 
Возможные последствия реализации угрозы – Инфицирование 
сервера, утечка данных, отказ в обслуживании.  
3.2.2 Неактуальные угрозы  
Все остальные УБИ, не указанные в пункте 3.2.1 являются неактуальными. 
3.3 Политика безопасности организации  
Объект оценки должен следовать приведенным ниже правилам политики 
безопасности организации.   
P.Authorized_Users  
Описание: к системе могут иметь доступ только уполномоченные 
пользователи, у которых есть права доступа к информации в ней.  
Цель: Ограничение круга лиц, имеющих возможность взаимодействовать 
с ОО, до тех, кто получил официальное разрешение.  
Реализация: Должна быть реализована система аутентификации 
(например, логин/пароль), а также механизм авторизации, определяющий 
уровень доступа каждого пользователя. 
13  
P.Need_To_Know 
Описание: доступ к информации в защищаемых ресурсах, а также 
возможность её модификации или удаления, должен предоставляться только тем 
пользователям, которым эта информация необходима для выполнения их 
служебных обязанностей. 
Цель: обеспечение принципа минимизации прав доступа, при котором 
каждый пользователь получает доступ только к тем данным и функциям, 
которые необходимы ему для работы. 
Реализация: должна быть реализована система разграничения доступа на 
основе ролей или политик безопасности, включающая контроль доступа к 
объектам, 
журналирование 
операций 
предоставления доступа на каждом уровне. 
P.Accountability  
и проверку необходимости 
Описание: Все действия пользователей в системе должны быть 
прослеживаемы и фиксируемы.  
Цель: Обеспечить ответственность за действия, выполняемые в системе, и 
поддержку аудита инцидентов.  
Реализация: Реализация механизма логирования всех значимых событий, 
таких как вход в систему, изменение данных, запросы к БД, ошибки 
аутентификации и т. д. Логи должны храниться в защищённом виде и быть 
доступны администраторам.  
P.Warn  
Описание: Объект оценки должен предупреждать пользователей 
относительно ответственности за несанкционированное использование ОО.  
Цель: Повышение осведомлённости пользователей о последствиях 
несанкционированного доступа и использования системы.  
Реализация: При каждом входе в систему должно отображаться 
информационное сообщение о том, что использование системы разрешено 
14  
только авторизованным персоналом, и любые несанкционированные действия 
будут зарегистрированы и могут повлечь юридическую ответственность.  
P.Credential_Protection  
Описание: Учётные данные пользователей должны храниться и 
передаваться безопасным образом.  
Цель: Защитить пароли, токены и другие средства аутентификации от 
перехвата, восстановления или подмены.  
Реализация: Пароли должны храниться в зашифрованном виде (например, 
с использованием bcrypt, Argon2); Передача данных между клиентом и сервером 
должна осуществляться по защищённому каналу (SSL/TLS); Необходимо 
использовать одноразовые токены и механизмы двухфакторной аутентификации  
15  
4 ЦЕЛИ БЕЗОПАСНОСТИ  
4.1 Цели безопасности для ОО  
В данном разделе описаны цели безопасности, направленные на защиту 
самого объекта оценки — информационной системы, реализующей 
авторизацию, регистрацию, аренду места и взаимодействие с базой данных.  
O.Authorization  
Функции безопасности объекта оценки (ОО) должны обеспечивать доступ 
к системе  только  
уполномоченным  
процедуру аутентификации.   
O.Credentials_Protection  
пользователям,  прошедшим 
ФБО должны обеспечивать конфиденциальность аутентификационной 
информации (логины, пароли), включая её хранение и передачу.  
O.Access_Control  
ФБО должны реализовывать механизм разграничения прав доступа, 
позволяющий предоставлять пользователям минимально необходимые 
привилегии.  
O.Logging  
ФБО должны предусматривать возможность регистрации событий 
безопасности, таких как: попытки входа; ошибки аутентификации; обращения к 
БД; изменения в настройках.  
O.Error_Handling  
ФБО должны исключать вывод в интерфейс пользователя подробной 
внутренней информации об ошибках, которая может содержать чувствительные 
данные.   
4.2 Цели безопасности для среды функционирования ОО  
16  
В данном разделе описаны цели безопасности, относящиеся к окружению, 
в котором работает ОО.  
OE.Install  
Должна быть обеспечена корректная установка, настройка и обновление 
ОО в соответствии с предоставленной документацией и рекомендациями по 
безопасности.  
OE.Credentials  
Должны быть предусмотрены меры по защите всей аутентификационной 
информации (логины, пароли, токены), используемой в системе.  
OE.Trusted_Load  
Должна быть обеспечена загрузка ОО в доверенной среде, исключающей 
несанкционированное изменение исполняемых файлов, библиотек и параметров 
запуска.  
17  
5 ТРЕБОВАНИЯ БЕЗОПАСНОСТИ ИТ  
В этом разделе ЗБ изложены функциональные требования и требования к 
доверию, 
которым 
должен соответствовать объект оценки (ОО). 
Функциональные требования, указанные в данном ЗБ, базируются на 
функциональных компонентах, приведённых в части 2 Основного документа 
(ОК). Требования доверия опираются на компоненты требований доверия из 
части 3 ОК и представлены в настоящем ЗБ в форме уровня доверия ОУД1. В 
качестве минимально допустимого уровня стойкости для функций безопасности, 
реализуемых вероятностными или перестановочными методами, установлен 
средний уровень СФБ.  
5.1 Функциональные требования безопасности ОО  
Функциональные компоненты из части 2 ОК, на которых основаны 
функциональные требования безопасности ОО, приведены в таблице 5.1.  
Далее будут представлены функциональные требования безопасности, 
разбитые на классы:  
Класс FCS – Криптографические функции, включает требования к 
реализации 
криптографических 
функций, 
обеспечивающих 
конфиденциальность, целостность и подлинность данных и взаимодействий 
внутри системы. Сюда относятся алгоритмы шифрования, хэширования, 
генерации ключей и цифровой подписи. Уровень криптографической защиты 
определяется выбранными алгоритмами, их параметрами и способом 
применения в системе.  
Класс FIA – Идентификация и аутентификация, объединяет требования к 
процессам идентификации и аутентификации пользователей и других субъектов 
18  
системы. Обеспечивает защиту от несанкционированного доступа путем 
проверки подлинности перед предоставлением прав на использование ресурсов 
системы. Поддерживает различные методы аутентификации, включая пароли, 
одноразовые токены и двухфакторную аутентификацию  
Класс FDP – Поток данных пользователя, объединяет требования по 
контролю доступа к данным и управлению информационными потоками между 
субъектами и объектами системы. Обеспечивает защиту пользовательских 
данных от несанкционированного доступа, модификации и утечки как при 
хранении, так и при передаче. Включает реализацию политик дискреционного и 
мандатного контроля доступа.  
Класс FPT – Защита функций безопасности, объединяет требования к 
обеспечению устойчивости и целостности механизмов безопасности системы. 
Предотвращает их обход, несанкционированное изменение или вывод из строя. 
Обеспечивает надёжное выполнение функций безопасности даже в условиях 
внешних воздействий или ошибок.  
Класс FTA – Доверенный маршрут/канал, объединяет требования к 
организации доверенного пути или канала между пользователем и безопасной 
частью системы. Обеспечивает защиту от прослушивания, модификации и 
подмены данных при взаимодействии через открытые или потенциально 
небезопасные среды, особенно важен в сетевых и удалённых сценариях.  
Таблица 5.1 - Функциональные компоненты, на которых основаны ФТБ ОО  
Идентификатор компонента 
требований  
Название компонента требований  
FCS_COP.l  
Криптографические операции  
FIA_AFL.l  
Обработка отказов аутентификации  
FIA_UAU.2  
Аутентификация до любых действий пользователя  
19  
Продолжение таблицы 5.1 
Идентификатор компонента 
требований  
Название компонента требований  
FIA_UID.2  
Идентификация до любых действий пользователя  
FDP_DAU.1  
Базовая аутентификация данных  
FPT_TST.1  
Тестирование ФБО  
FTA_SSL.2  
Блокирование, инициированное пользователем  
FTA_TAB.l  
Предупреждения по умолчанию перед 
предоставлением доступа к ОО  
5.1.1 Класс FCS – Криптографические функции  
FCS_COP.1 – Приложение реализует криптографическую защиту учетных 
данных пользователей. Пароль пользователя всегда хэшируется с 
использованием алгоритма MD5 перед сохранением в базу данных. Это 
обеспечивает 
защиту 
от 
раскрытия 
несанкционированного доступа к файлу БД.  
паролей 
даже 
5.1.2 Класс FIA – Идентификация и аутентификация  
в 
случае 
FIA_AFL.1 – Система ограничивает количество попыток входа: после трёх 
неудачных попыток авторизации аккаунт блокируется на 5 минут. Это 
предотвращает автоматизированные атаки на учетные записи, такие как 
брутфорс. Пользователь получает соответствующее уведомление и может 
повторить попытку позже.  
FIA_UAU.2 – Система не предоставляет доступ к основному функционалу 
без успешной аутентификации. При запуске приложения пользователь 
направляется на экран регистрации или авторизации. Только после 
20  
подтверждения личности он может перейти в главное меню и выполнять 
действия, такие как выбор зала, выбор места, аренда и просмотр истории.   
FIA_UID.2 – Перед выполнением любого действия в системе пользователь 
обязательно проходит этап идентификации, вводя уникальный логин. Система 
проверяет наличие такого логина в базе данных и сопоставляет его с введенным 
паролем для дальнейшей авторизации.  
5.1.3 Класс FPT – Защита функций безопасности  
FPT_TST.1 – Компонент требует проведения тестирования всех функций 
безопасности. В рамках проекта проводилось тестирование подсистемы 
авторизации, проверка хэширования паролей, проверка аренды места и 
просмотр данных в базе данных. Все найденные ошибки фиксировались и 
исправлялись, что позволило повысить надёжность и безопасность приложения.  
5.1.4 Класс FTA – Доверенный маршрут/канал  
FTA_SSL.2 – Компонент требует возможности завершения сеанса по 
инициативе пользователя. В приложении реализована кнопка выхода 
(«Выход»), которая приводит к завершению текущей сессии, очистке 
временных данных и возврату к окну авторизации. Это позволяет пользователю 
контролировать 
своё 
присутствие 
в 
системе 
несанкционированный доступ со стороны третьих лиц.  
и предотвращает 
FTA_TAB.1 – Данный компонент требует, чтобы доступ к системе 
предоставлялся только после прохождения процедуры идентификации и 
аутентификации. В приложении «АИС для компьютерного клуба» пользователь 
сразу после запуска программы направляется на экран регистрации или 
21  
авторизации, где он должен ввести свои учетные данные. Без этого дальнейший 
доступ к функционалу системы невозможен, что полностью соответствует 
данному требованию.  
5.2 Требования доверия к безопасности (ТБД)  
Требования доверия к безопасности объекта оценки (ОО), которые 
представлены в таблице 5.2, взяты из части 3 общих критериев (ОК) и реализуют 
уровень доверия ОУД1 (основной уровень доверия). Они направлены на 
обеспечение надежности, целостности и защищенности системы на этапах 
разработки, тестирования, документирования и доставки.  
Компонент  
Описание  
Таблица 5.2 – Требования доверия к безопасности (ТДБ)  
Класс  
ACM   
ACM_CAP.1  Управление конфигурацией, контроль версий и 
изменений  
ADO  
ADO_IGS.1  
Безопасность установки, генерации и запуска 
программы  
ADV  ADV_FSP.1  Неформальная функциональная спецификация  
ADV  ADV_RCR.1  Неформальное соответствие требованиям  
AGD_ADM.1  Руководство администратора  
AGD  
AGD  
AGD_USR.1  Руководство пользователя  
ALC  
ALC_CMC.1  Маркировка ОО (например, версия 1.0)  
ALC  
ALC_CMS.1  Список элементов конфигурации (код, файл БД)  
ALC  
ALC_DEL.1  Процедуры доставки программного обеспечения  
ASE  
ASE_CCL.1  Утверждения о соответствии стандартам  
ASE  
ASE_INT.1  
Введение в ЗБ, включая идентификацию  
22  
Продолжение таблицы 5.2  
ASE  
ASE_OBJ.1  
Определение целей безопасности  
ASE  
ASE_REQ.1  Формулировка требований безопасности  
ASE  
ASE_TSS.1  
Краткая спецификация ФБО и ТДБ  
ATE  
ATE_IND.1  
Независимое тестирование  
AVA  
AVA_SOF.1  
Оценка стойкости функции безопасности  
Класс ACM – Управление конфигурацией  
Этот класс включает требования по контролю состава системы и её 
компонентов.  
ACM_CAP.1 – Возможности управления конфигурацией  
В процессе разработки обеспечивается контроль версий программного 
обеспечения и его компонентов. Используется система контроля версий 
(например, GitHub), позволяющая отслеживать изменения в коде, а также 
сохранять историю модификаций и обеспечивать целостность сборок.  
Класс ADO – Доставка и эксплуатация  
Класс содержит требования к безопасному распространению и установке 
программного обеспечения.  
ADO_IGS.1 – Безопасность установки, генерации и запуска  
Приложение «АИС для компьютерного клуба» распространяется в виде 
готовой установочной сборки или исполняемого файла (.exe). Все файлы 
доставляются в защищённом формате, исключающем возможность их 
несанкционированного изменения до момента установки или запуска на стороне 
пользователя.  
Класс ADV – Разработка  
Этот класс объединяет требования к документированию архитектуры и 
функциональных возможностей системы.  
ADV_FSP.1 – Неформальная функциональная спецификация  
23  
Архитектура приложения и его основные модули описаны в проектной 
документации: UML-диаграммы, блок-схемы алгоритма аренды, описание 
подсистем (GUI, авторизация, аренда, БД).  
ADV_RCR.1 – Неформальное соответствие требованиям  
Реализация всех функций выполнена в соответствии с техническим 
заданием. Проведено сравнение между заявленными требованиями и 
фактической реализацией, подтверждающее их полное выполнение.  
Класс AGD – Руководства  
Класс включает требования к наличию и качеству документации для 
пользователей и администраторов.  
AGD_ADM.1 – Руководство администратора  
Подготовлено руководство, содержащее информацию по настройке и 
обслуживанию системы, описание структуры базы данных, рекомендации по 
обновлению и масштабированию.  
AGD_USR.1 – Руководство пользователя  
Создано пошаговое руководство пользователя, включающее инструкции 
по регистрации, авторизации, выбора зала, выбора места, выполнению аренды и 
просмотру истории.  
Класс ALC – Поддержка жизненного цикла  
Этот класс охватывает процессы маркировки, доставки и управления 
элементами конфигурации.  
ALC_CMC.1 – Маркировка ОО  
Программный продукт имеет четко обозначенную версию (например, 
версия 1.0), что позволяет однозначно идентифицировать текущую сборку.  
ALC_CMS.1 – Список элементов конфигурации  
Для проекта ведётся список компонентов системы: исходный код на C#, 
файлы графического интерфейса, файл базы данных SSMS, ресурсы 
приложения.  
ALC_DEL.1 – Процедуры доставки программного обеспечения  
24  
Финальная сборка приложения предоставляется через защищённый 
репозиторий (GitHub), где осуществляется проверка целостности и подлинности 
передаваемых файлов.  
Класс ASE – Заявления о безопасности  
Этот класс включает требования к формулированию и утверждению 
заявлений о безопасности.  
ASE_CCL.1 – Утверждения о соответствии стандартам  
Разработаны утверждения о соответствии системы требованиям 
безопасности, включая защиту учетных данных, шифрование паролей и защиту 
от SQL-инъекций.  
ASE_INT.1 – Введение в ЗБ, включая идентификацию  
В пояснительной записке представлено введение в защиту информации, 
описаны ключевые компоненты системы, её назначение и область применения.  
ASE_OBJ.1 – Определение целей безопасности  
Цели безопасности определены как обеспечение конфиденциальности 
учетных данных, целостности данных пользователя и доступности приложения.  
ASE_REQ.1 – Формулировка требований безопасности  
В техническом задании и пояснительной записке сформулированы 
конкретные требования безопасности: защита от несанкционированного 
доступа, хранение паролей в зашифрованном виде, ограничение попыток входа.  
ASE_TSS.1 – Краткая спецификация ФБО и ТДБ  
Представлена краткая спецификация функций безопасности объекта и 
требований доверия к безопасности, включая описание компонентов 
FCS_COP.1, FIA_AFL.1 и других.  
Класс ATE – Тестирование  
Класс включает требования к проверке корректности реализации функций 
безопасности.  
ATE_IND.1 – Независимое тестирование  
25  
Все функции безопасности были протестированы на различных этапах 
разработки. Проведено тестирование регистрации, авторизации, шифрования 
паролей, защиты от SQL-инъекций и работы с числовыми массивами.  
Результаты тестирования оформлены в отчёте.  
Класс AVA – Оценка уязвимостей  
Этот класс связан с анализом устойчивости системы к потенциальным 
угрозам.  
AVA_SOF.1 – Оценка стойкости функции безопасности  
Проведён анализ уязвимостей системы. Проверены такие аспекты, как 
защита паролей, ограничение количества попыток входа, защита от SQL 
инъекций и корректная обработка ошибок. Выявленные слабые места 
исправлены, что позволило повысить уровень доверия к системе.  
5.3 Требования безопасности для среды ИТ  
Для обеспечения безопасной работы системы в рамках среды 
информационных технологий (ИТ) реализованы следующие требования, взятые 
из стандарта Common Criteria / ISO/IEC 15408:  
FIA_AFL.1 – Блокировка учётной записи после 3 неуспешных попыток 
аутентификации  
Приложение ограничивает количество попыток входа: после трёх 
неудачных попыток авторизации учетная запись блокируется на 5 минут. Это 
предотвращает автоматизированные атаки типа брутфорс. Пользователь 
получает уведомление о блокировке и может повторить попытку входа по 
истечении установленного времени.  
FIA_UAU.2 – Обязательная аутентификация перед действиями  
Доступ ко всем функциям системы возможен только после успешной 
аутентификации. При запуске приложения пользователь направляется на экран 
26  
регистрации или авторизации. Только после подтверждения личности ему 
предоставляется доступ к основному функционалу: выбор зала, выбор места, 
аренда, просмотр истории и т.д.  
FPT_RVM.1 – Невозможность обхода политики безопасности  
Реализация политик безопасности (например, проверка прав пользователя, 
защита от несанкционированного доступа к данным) выполнена таким образом, 
что обойти их невозможно без изменения исходного кода. Все проверки 
проводятся до выполнения операций, и нет способа получить доступ к 
защищённым ресурсам минуя механизм аутентификации и авторизации.  
FPT_TST.1 – Самотестирование ФБО при запуске приложения  
При запуске приложения выполняется проверка корректности работы 
ключевых функций безопасности, таких как: Подключение к базе данных; 
Возможность хэширования паролей; Проверка наличия необходимых файлов 
конфигурации.  
Это позволяет заранее выявить ошибки, которые могут повлиять на 
безопасность системы, и сообщить пользователю о проблемах до начала 
использования приложения.  
27  
6 КРАТКАЯ СПЕЦИФИКАЦИЯ ОО  
6.1 Функции безопасности ОО  
Ниже представлены реализованные функции безопасности, относящиеся 
к объекту оценки (ОО) — вашему приложению «АИС для компьютерного 
клуба». Все функции соответствуют требованиям из Common Criteria (Common 
Criteria v2.1: Полнота и согласованность требований) и реализованы в рамках 
технического задания.  
Аутентификация (FIA_UAU.2, FIA_AFL.1, FIA_UID.2).  
Приложение 
реализует 
обязательную 
аутентификацию 
перед 
выполнением любых действий (FIA_UAU.2). Пользователь должен ввести 
уникальный логин (FIA_UID.2), который однозначно его идентифицирует. 
Пароль хранится в зашифрованном виде с использованием алгоритма MD5. 
После трёх неудачных попыток входа аккаунт блокируется на 5 минут 
(FIA_AFL.1), что предотвращает брутфорс-атаки.  
Управление доступом (FDP_ACC.1, FDP_ACF.1, FDP_IFF.1)  
Доступ к функционалу системы ограничен: только авторизованные 
пользователи могут выполнять аренду компьютерного места и просматривать 
историю аренды. В системе реализованы политики контроля доступа, которые 
проверяют права пользователя перед выполнением операций (FDP_ACF.1, 
FDP_ACC.1). Входные данные проверяются на корректность и безопасность 
перед обработкой (FDP_IFF.1).  
Устойчивость (FPT_FLS.1, FPT_ITT.1, FPT_TST.1, FRU_FLT.2)  
При возникновении ошибок (например, некорректный ввод, сбой БД) 
система возвращается в безопасное состояние, не позволяя продолжить работу в 
непредвиденном режиме (FPT_FLS.1). Обеспечена защита внутренней передачи 
28  
данных между компонентами приложения (FPT_ITT.1). Также реализовано 
самотестирование ключевых функций безопасности при запуске  
(FPT_TST.1). Система устойчива к временным перебоям в ресурсах 
(FRU_FLT.2).  
Приватность (FPR_UNO.1)  
Приложение минимизирует риск утечки информации через отладочные 
сообщения или журналы. Ошибки выводятся в общем виде без указания деталей, 
которые могут быть использованы злоумышленниками для анализа структуры 
системы или базы данных (FPR_UNO.1).  
6.2 Меры доверия к безопасности  
Для обеспечения уровня доверия к системе (ОУД1) реализованы 
следующие меры:  
Архитектура (ADV_RCR.1, ADV_FSP.1)  
Архитектура системы документирована: разработаны UML-диаграммы 
классов и прецедентов, блок-схемы алгоритмов, описание подсистем. 
Реализация 
соответствует 
(ADV_RCR.1).  
заявленным 
требованиям 
безопасности 
Архитектурные решения обеспечивают защиту от обхода функций 
безопасности (ADV_FSP.1).  
Руководства (AGD_USR.1, AGD_ADM.1)  
Подготовлено пошаговое руководство пользователя (AGD_USR.1), 
содержащее инструкции по регистрации, авторизации и работе с программой. 
Также создано руководство администратора (AGD_ADM.1), включающее 
информацию по настройке, обслуживанию и масштабированию системы.  
Конфигурация (ACM_CAP.1, ADO_IGS.1, ALC_CMC.1, ALC_CMS.1, 
ALC_DEL.1)  
29  
Для проекта используется система контроля версий (GitHub), где 
фиксируются изменения кода (ACM_CAP.1). Приложение распространяется в 
виде готовой сборки (.exe), защищённой от несанкционированного изменения до 
запуска (ADO_IGS.1). Программный продукт имеет маркировку версии 
(например, 1.0) (ALC_CMC.1). Список элементов конфигурации включает 
исходный код, файлы GUI, БД SSMS (ALC_CMS.1). Финальная сборка 
доставляется через защищённый репозиторий с проверкой целостности файлов 
(ALC_DEL.1).  
Тестирование (ATE_IND.1, AVA_SOF.1)  
Все 
функции безопасности были протестированы независимо: 
регистрация, авторизация, шифрование паролей, защита от SQL-инъекций 
(ATE_IND.1). Выполнен анализ уязвимостей системы: проверены защита 
паролей, обработка ошибок, работа с данными (AVA_SOF.1). Выявленные 
слабые места исправлены.    
30  