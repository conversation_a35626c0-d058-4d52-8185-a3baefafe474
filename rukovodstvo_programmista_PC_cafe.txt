Министерство науки и высшего образования Российской Федерации 
Федеральное государственное автономное образовательное учреждение 
высшего образования 
ТОМСКИЙ ГОСУДАРСТВЕННЫЙ УНИВЕРСИТЕТ СИСТЕМ 
УПРАВЛЕНИЯ И РАДИОЭЛЕКТРОНИКИ (ТУСУР) 
Кафедра комплексной информационной безопасности электронно-вычислительных систем (КИБЭВС) 

АИС ДЛЯ КОМПЬЮТЕРНОГО КЛУБА 
Руководство программиста  

«Проектирование защищенных телекоммуникационных систем» 

Исполнитель 
Студент гр. 721-1 
__________ Власов В.А. 
__________ 2025 г. 

Принял 
м.н.с каф. КИБЭВС 
__________ Ковалев И.В. 
__________ 2025 г. 

Томск 2025 

Перечень сокращений 
АИС – автоматизированная информационная система; 
БД – база данных; 
ОС – операционная система; 
СУБД – система управления базами данных;
GUI – графический интерфейс пользователя;
.NET – платформа Microsoft .NET Framework;
C# – язык программирования C#;
PC – персональный компьютер.

2  

1 Назначение и условия применения программы 

АИС «Компьютерный клуб» предназначена для автоматизации основных процессов 
компьютерного клуба: авторизации и регистрации пользователей, аренды компьютерного 
места, учета посетителей компьютерного клуба, управления сеансами пользователей, 
а также учета информации о компьютерных конфигурациях и залах.

1.1 Требования к техническим средствам 

Для работы «АИС для компьютерного клуба» необходим один персональный 
компьютер с конфигурацией не ниже:  
1) ПК x64, 2+ ядра, 4 ГБ ОЗУ и более. 
2) Свободное место на диске от 500 МБ (без учёта БД). 

1.2 Требования к общему программному обеспечению (ОПО) 

Требования к общему программному обеспечению (ОПО), 
необходимому для «АИС для компьютерного клуба», представлены в таблице 1.  

Таблица 1. ПК с приложением (клиент) 

Компонент                    Требование 
ОС                          Windows 10/11 x64 
.NET                        .NET Framework 4.7.2+ (Windows Forms, ADO.NET в составе) 
Конфигурация приложения     Строка подключения в App.config для подключения к БД 
                           (указать сервер БД, каталог, режим аутентификации) 
Сеть (при удалённой БД)     Доступ по TCP/IP к серверу БД (порт 1433/TCP; 1434/UDP для SQL 
                           Browser, если используется именованный инстанс) 
Права в БД                  Доступ на чтение/изменение таблиц предметной области 
Администрирование (опц.)    SSMS для диагностики, при наличии прав 

3  

Таблица 2. ПК с сервером БД (SQL Server) 

Компонент                   Требование 
ОС                         Windows Server 2016/2019/2022 или Windows 10/11 x64 
СУБД                       Microsoft SQL Server 2016/2017/2019/2022 (Express/Standard/Developer) 
Сетевые протоколы          Включён TCP/IP в SQL Server Configuration Manager 
Обнаружение инстанса       Для именованного инстанса — служба SQL Server Browser = Running 
Брандмауэр                 Разрешить 1433/TCP (статический порт инстанса) 
                          и 1434/UDP (SQL Browser) либо настроенные порты, если нестандартные 
Аутентификация             Windows Authentication (Integrated) и/или SQL Authentication (логин/пароль) 
База данных                Развёрнута БД PC-cafe (схема таблиц Пользователь/Место/Аренда/Аренда_места) 
Резервное копирование      Настроены задания Backup/Restore по регламенту 
Мониторинг/аудит (опц.)    Средства мониторинга SQL Server, аудит входов и изменений по политике ИБ 

Требования к периферийным устройствам, необходимому для «АИС для 
компьютерного клуба», представлены в таблице 3.  

Таблица 3. Требования к периферийным устройствам 

Наименование                                    Количество 
Клавиатура                                     1 
Мышь                                          1 
Система обеспечения бесперебойного питания     1 

4  

2 Характеристика программы

АИС «Компьютерный клуб» реализована как настольное приложение Windows
Forms (.NET Framework 4.7.2+) с использованием ADO.NET для доступа к Microsoft SQL Server
по строке подключения, настроенной в App.config.

Состав модулей:
• Авторизация (LogForm): ввод учётных данных с placeholder-текстом, хеширование
паролей с использованием MD5 (класс HashPass), проверка учетных данных в БД.
• Регистрация (RegForm): создание новых учётных записей пользователей с полной
информацией (логин, пароль, ФИО), проверка уникальности логина, валидация полей.
• Главное окно (Main1): навигация к разделам выбора залов (Low Cost, Base, Premium),
отображение информации о пользователе через контекстное меню, интерактивные
элементы с эффектами наведения.
• Формы залов (LowForm, BaseForm, PremForm): отображение доступных компьютеров
(ID 1-2 для Low, 3-4 для Base, 5-6 для Premium), их конфигураций, стоимости аренды,
статуса доступности, возможность выбора времени и бронирования места.
• Система аренды (Rent): обработка процесса аренды компьютерного места с расчетом
прибыли (90% от стоимости), создание записей в таблицах Аренда и Аренда_места.
• Информация о ПК (PCInfo): получение данных о конфигурации, стоимости и
доступности компьютеров из БД.
• Информация о пользователе (UserInfo): хранение данных текущего пользователя
(ID, логин, ФИО) для передачи между формами.
• Работа с БД (DataBase): управление подключением к базе данных SQL Server,
методы открытия/закрытия соединения.

Модель данных:
• Основные сущности:
  - Пользователь (ID_пользователя, Логин, Пароль, Фамилия, Имя, Отчество)
  - Место (ID_места, Комплектующие, Стоимость_в_час, Доступность)
  - Аренда (ID_аренды, ID_пользователя, Прибыль, Время_начала)
  - Аренда_места (ID_аренды, ID_места, Время_окончания, Прибыль)
• Связи: Аренда связана с Пользователем через ID_пользователя;
  Аренда_места связана с Арендой через ID_аренды и с Местом через ID_места.
• Распределение мест по залам: места 1-2 (Low Cost), 3-4 (Base), 5-6 (Premium).

Пользовательский интерфейс:
• Использование Windows Forms с графическими элементами (PictureBox, Label, Button).
• Интерактивные эффекты: изменение изображений при наведении мыши.
• Placeholder-текст в полях ввода с автоматическим изменением цвета.
• Контекстные меню для отображения информации о пользователе.
• Центрирование форм на экране для удобства использования.

Безопасность и надёжность:
• Хеширование паролей с использованием алгоритма MD5 перед сохранением в БД.
• Использование параметризованных SQL-запросов для предотвращения инъекций.
• Проверка уникальности логинов при регистрации.
• Валидация входных данных с визуальной индикацией ошибок (красный цвет).
• Обработка исключений с выводом пользовательских сообщений об ошибках.
• Контроль состояния подключения к БД перед выполнением операций.

Конфигурация:
• Подключение к БД задаётся строкой подключения в классе DataBase
  (пример: "Data Source=LAPTOP-4SSECJ20\MSSQLSERVER01;initial catalog=PC-cafe;Integrated Security=True").
• Использование Integrated Security для аутентификации Windows.
• Ресурсы приложения (изображения) хранятся в папке Resources.

Ограничения и предположения:
• Локальное или сетевое развертывание SQL Server с именованным инстансом.
• Фиксированное количество компьютеров (по 2 в каждом зале).
• Проверки данных базовые (наличие обязательных полей, корректность ID).
• Детальные бизнес-правила реализуются на уровне приложения.
• Жестко заданные пути к ресурсам в коде (требует корректировки при развертывании).

5  

3 Обращение к программе 

3.1 Загрузка и запуск программы 

Для запуска «АИС для компьютерного клуба» требуется установить нужные 
программы и зависимости.  

1) Установить СУБД SQL Server (Developer Edition) с официального сайта Microsoft; 
2) Установить SQL Server Management Studio (SSMS) с официального сайта; 
3) Создать базу данных PC-cafe и необходимые таблицы согласно схеме БД; 
4) Установить .Net Framework 4.7.2 или выше c официального сайта Microsoft; 
5) Установить Microsoft Visual Studio 2022 Community Edition с официального сайта 
   (для разработки и отладки); 
6) Скачать и распаковать архив с программой «АИС для компьютерного клуба»; 
7) Настроить корректную строку подключения в App.config 
   (пример: Data Source=LAPTOP-4SSECJ20\MSSQLSERVER01;initial catalog=PC-cafe;Integrated Security=True); 
8) Запустить приложение (PC_cafe.exe или из Visual Studio). 

3.2 Выполнение программы

Описание работы «АИС для компьютерного клуба»:

Этап 1. Авторизация и регистрация
1) При запуске приложения открывается форма авторизации (LogForm) с полями для ввода
   логина и пароля, содержащими placeholder-текст.
2) Пользователь может:
   - Войти в систему, введя существующие логин и пароль
   - Перейти к регистрации, нажав кнопку «Регистрация»
3) При регистрации (RegForm) пользователь заполняет:
   - Логин (проверяется уникальность)
   - Пароль (хешируется MD5 перед сохранением)
   - ФИО (фамилия, имя, отчество)
4) После успешной регистрации происходит возврат к форме авторизации.

Этап 2. Главное меню и навигация
5) После успешной авторизации открывается главное меню (Main1) с:
   - Тремя интерактивными изображениями залов с эффектами наведения
   - Иконкой пользователя с контекстным меню (правая кнопка мыши)
6) Пользователь выбирает один из залов:
   - Low Cost (места 1-2)
   - Base (места 3-4)
   - Premium (места 5-6)

Этап 3. Выбор и аренда компьютера
7) В форме зала (LowForm/BaseForm/PremForm) отображаются:
   - Два компьютера с интерактивными изображениями
   - При клике на компьютер: конфигурация, стоимость в час, статус доступности
8) Если компьютер доступен, пользователь может:
   - Выбрать время аренды из выпадающего списка (ComboBox)
   - Нажать кнопку «Арендовать»
9) Система выполняет:
   - Расчет стоимости аренды
   - Создание записи в таблице «Аренда»
   - Создание записи в таблице «Аренда_места»
   - Расчет прибыли (90% от стоимости)
10) При успешной аренде выводится сообщение и происходит возврат в главное меню.

Этап 4. Завершение сеанса
11) Пользователь может завершить работу:
    - Закрытием текущей формы (возврат к предыдущей)
    - Закрытием главного окна (завершение приложения)

3.3 Завершение работы программы 

Для завершения работы «АИС для компьютерного клуба» нужно: 
1) Нажать на красную кнопку «×» в правом верхнем углу окна; 
2) Или закрыть все открытые формы приложения. 
При закрытии главной формы приложение завершает работу полностью. 

6  

4 Входные и выходные данные

Входными данными для системы являются информация, вводимая пользователем
через интерфейс приложения АИС, а также данные, получаемые из базы данных.

Выходными данными являются информация, отображаемая пользователю,
и данные, сохраняемые в базе данных.

Список входных и выходных данных представлен в таблице 4.

Таблица 4. Список входных и выходных данных.

Входные данные                                  Выходные данные

Авторизация пользователя
Логин пользователя (строка)                    Результат проверки в БД (найден/не найден)
Пароль пользователя (строка)                   MD5-хеш пароля для сравнения с БД
Данные из таблицы «Пользователь»               Объект UserInfo с полной информацией о пользователе
                                              (ID, логин, фамилия, имя, отчество)

Регистрация нового пользователя
Логин (строка, уникальный)                     Проверка уникальности в БД
Пароль (строка)                               MD5-хеш для сохранения в БД
Фамилия (строка)                              Запись в поле «Фамилия» таблицы «Пользователь»
Имя (строка)                                  Запись в поле «Имя» таблицы «Пользователь»
Отчество (строка)                             Запись в поле «Отчество» таблицы «Пользователь»
Новая запись в БД                             ID нового пользователя (IDENTITY)

Информация о компьютерных местах
ID места (целое число: 1-6)                   Данные из таблицы «Место»:
                                              - Комплектующие (описание конфигурации)
                                              - Стоимость в час (decimal)
                                              - Доступность (boolean)
Запрос доступности места                       Статус «Занят» или «Доступен»

Процесс аренды
ID пользователя (целое число)                  Новая запись в таблице «Аренда»
ID места (целое число)                         Связь в таблице «Аренда_места»
Время начала аренды (DateTime.Now)             Поле «Время_начала» в таблице «Аренда»
Время окончания аренды (DateTime)              Поле «Время_окончания» в таблице «Аренда_места»
Выбранное время аренды (часы)                  Расчет стоимости (время × стоимость_в_час)
Стоимость аренды (decimal)                     Расчет прибыли (90% от стоимости)
Данные аренды                                 ID новой аренды (IDENTITY)

Отображение информации пользователю
Данные пользователя из UserInfo                Отображение в контекстном меню:
                                              - Логин пользователя
                                              - ФИО в формате «Фамилия\nИмя\nОтчество»
Конфигурация компьютера                        Текстовое описание в интерфейсе
Стоимость аренды                              Числовое значение в рублях за час
Статус доступности                            Текст «Доступен» или «Занят»
Результат аренды                              Сообщение об успехе или ошибке

Конфигурационные данные
Строка подключения к БД                        Соединение с SQL Server
Пути к ресурсам (изображения)                  Загрузка графических элементов интерфейса
Параметры подключения                          Статус соединения (Open/Closed)

Данные для отчетности
История аренды пользователя                    Записи из связанных таблиц «Аренда» и «Аренда_места»
Статистика использования мест                  Данные о занятости компьютеров
Финансовые данные                             Расчет прибыли от аренды

7  

5 Сообщения

5.1 Сообщения оператору

1. Авторизация (LogForm)
• При неверной паре логин/пароль: отображение текста в logErrorText с красным цветом
• При пустых полях логина: изменение цвета logLogBox.ForeColor на красный
• При пустых полях пароля: изменение цвета logPassBox.ForeColor на красный
• Placeholder-текст для логина: «Введите логин» (серый цвет)
• Placeholder-текст для пароля: «Введите пароль» (серый цвет)

2. Регистрация (RegForm)
• При пустом поле логина: изменение regLogBox.ForeColor на красный
• При пустом поле пароля: изменение regPassBox.ForeColor на красный
• При пустом поле фамилии: изменение SnameLabel.ForeColor на красный
• При пустом поле имени: изменение FnameLabel.ForeColor на красный
• При пустом поле отчества: изменение TnameLabel.ForeColor на красный
• При успешной регистрации: автоматический переход к форме авторизации
• При ошибке БД: отображение сообщения с деталями исключения
• Placeholder-тексты: «Введите логин», «Введите пароль», «Введите имя»,
  «Введите фамилию», «Введите отчество» (серый цвет)

3. Главное окно (Main1)
• Контекстное меню пользователя (правая кнопка мыши):
  - Пункт «Логин и ФИО» с подпунктами:
  - Логин пользователя (_userInfo._userLogin)
  - ФИО в формате «Фамилия\nИмя\nОтчество»
• ToolTip при наведении на иконку пользователя:
  «Нажмите правую кнопку мыши для получения подробностей»
• Визуальные эффекты при наведении на залы: смена изображений на подсвеченные версии

4. Формы залов (LowForm, BaseForm, PremForm)
• При выборе доступного компьютера:
  - Отображение конфигурации в labelConf (данные из PCInfo._PC_Conf)
  - Отображение стоимости в labelCost (данные из PCInfo._PC_Cost)
  - Отображение статуса и времени в labelStatusANDTime
• При недоступности компьютера:
  - Текст «Занят» в labelStatusANDTime
  - Скрытие элементов выбора времени (ComboBox) и кнопки аренды
• При успешной аренде: MessageBox с текстом
  «Аренда успешна, производится выход в главное меню»
• При расчете стоимости: отображение итоговой суммы на основе выбранного времени

5. Система аренды (Rent)
• При ошибке создания записи в таблице Аренда:
  Exception с сообщением «Упс...»
• При ошибке создания записи в таблице Аренда_места:
  Exception с сообщением «Упс....»
• При успешной аренде: MessageBox
  «Аренда успешна, производится выход в главное меню»
• При ошибке удаления (откат транзакции):
  Exception с сообщением «Упс.....»

6. Информация о ПК (PCInfo)
• При ошибке получения данных о компьютере:
  Exception «Произошла ошибка, попробуйте перезагрузить приложение»
• Отображение конфигурации: текстовое описание комплектующих
• Отображение стоимости: числовое значение в формате decimal
• Отображение доступности: boolean значение (true/false)

7. База данных (DataBase)
• При ошибке подключения: стандартные исключения SqlConnection
• При ошибке выполнения запросов: SqlException с деталями ошибки
• Состояния соединения: Closed, Open (проверяется перед операциями)

8. Глобально (всё приложение)
• Необработанные исключения: отображение общих сообщений об ошибках
  без раскрытия внутренней структуры системы
• Ошибки валидации: визуальная индикация через изменение цветов элементов
• Системные сообщения: использование стандартных MessageBox для уведомлений

10

6 Схема базы данных

Структура базы данных «PC-cafe» включает следующие таблицы:

Таблица «Пользователь»
• ID_пользователя (int, PRIMARY KEY, IDENTITY) – уникальный идентификатор пользователя
• Логин (nvarchar) – уникальный логин пользователя
• Пароль (nvarchar) – хешированный пароль (MD5)
• Фамилия (nvarchar) – фамилия пользователя
• Имя (nvarchar) – имя пользователя
• Отчество (nvarchar) – отчество пользователя

Таблица «Место»
• ID_места (int, PRIMARY KEY) – уникальный идентификатор компьютерного места
• Комплектующие (nvarchar) – описание конфигурации компьютера
• Стоимость_в_час (decimal) – стоимость аренды за час
• Доступность (bit) – статус доступности места (true/false)

Таблица «Аренда»
• ID_аренды (int, PRIMARY KEY, IDENTITY) – уникальный идентификатор аренды
• ID_пользователя (int, FOREIGN KEY) – ссылка на пользователя
• Прибыль (decimal) – расчетная прибыль от аренды
• Время_начала (datetime) – время начала аренды

Таблица «Аренда_места»
• ID_аренды (int, FOREIGN KEY) – ссылка на аренду
• ID_места (int, FOREIGN KEY) – ссылка на место
• Время_окончания (datetime) – время окончания аренды
• Прибыль (decimal) – итоговая прибыль (90% от стоимости)

Связи между таблицами:
• Пользователь (1) → Аренда (M) через ID_пользователя
• Аренда (1) → Аренда_места (M) через ID_аренды
• Место (1) → Аренда_места (M) через ID_места

Распределение мест по залам:
• Low Cost зал: места с ID 1, 2
• Base зал: места с ID 3, 4
• Premium зал: места с ID 5, 6

11
