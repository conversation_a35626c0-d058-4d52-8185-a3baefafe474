﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace PC_cafe
{
    public partial class PremForm : Form
    {
        DataBase dataBase = new DataBase();
        PCInfo pCInfo1 = PCInfo.GetPCInfo(5);
        PCInfo pCInfo2 = PCInfo.GetPCInfo(6);
        decimal stonks1 = 0;
        decimal stonks2 = 0;
        private readonly UserInfo _userInfo;
        DateTime dateTime = DateTime.Now;
        DateTime dt = new DateTime();

        public PremForm(UserInfo userInfo)
        {
            InitializeComponent();
            StartPosition = FormStartPosition.CenterScreen;
            _userInfo = userInfo;
        }

        private void Prem_FormClosed(object sender, FormClosedEventArgs e)
        {
            Main1 main1 = new Main1(_userInfo);
            main1.Show();
        }

        private void pictureBoxPC13_Click(object sender, EventArgs e)
        {
            pictureBoxPC13.Width = 164;
            pictureBoxPC13.Height = 164;
            pictureBoxPC23.Width = 385;
            pictureBoxPC23.Height = 385;

            labelConf13.Visible = true;
            labelCost13.Visible = true;
            labelStatusANDTime3.Visible = true;
            if (labelStatusANDTime3.Text == "Занят")
            {
                comboBox13.Visible = false;
                buttonForRent13.Visible = false;
            }
            else if (labelStatusANDTime3.Text == "Свободен")
            {
                comboBox13.Visible = true;

                if (comboBox13.Text != "")
                {
                    buttonForRent13.Visible = true;
                    labelFinalCost13.Visible = true;
                }
                else
                {
                    buttonForRent13.Visible = false;
                    labelFinalCost13.Visible = false;
                }

            }

            labelConf23.Visible = false;
            labelCost23.Visible = false;
            labelStatusANDTime23.Visible = false;
            comboBox23.Visible = false;
            buttonForRent23.Visible = false;
            labelFinalCost23.Visible = false;
        }

        private void pictureBoxPC23_Click(object sender, EventArgs e)
        {
            pictureBoxPC23.Width = 164;
            pictureBoxPC23.Height = 164;
            pictureBoxPC13.Width = 385;
            pictureBoxPC13.Height = 385;

            labelConf13.Visible = false;
            labelCost13.Visible = false;
            labelStatusANDTime3.Visible = false;
            comboBox13.Visible = false;
            buttonForRent13.Visible = false;
            labelFinalCost13.Visible = false;

            labelConf23.Visible = true;
            labelCost23.Visible = true;
            labelStatusANDTime23.Visible = true;

            if (labelStatusANDTime23.Text == "Занят")
            {
                comboBox23.Visible = false;
                buttonForRent23.Visible = false;
            }
            else if (labelStatusANDTime23.Text == "Свободен")
            {
                comboBox23.Visible = true;
                if (comboBox23.Text == "")
                    buttonForRent23.Visible = false;
                else
                    buttonForRent23.Visible = true;
            }
        }

        private void PremForm_Load_1(object sender, EventArgs e)
        {
            labelConf13.Visible = false;
            labelCost13.Visible = false;
            labelStatusANDTime3.Visible = false;
            comboBox13.Visible = false;
            buttonForRent13.Visible = false;
            labelFinalCost13.Visible = false;

            labelConf23.Visible = false;
            labelCost23.Visible = false;
            labelStatusANDTime23.Visible = false;
            comboBox23.Visible = false;
            buttonForRent23.Visible = false;
            labelFinalCost23.Visible = false;

            labelConf13.Text = $"Комплектующие:\n{pCInfo1._PC_Conf}";
            labelCost13.Text = $"Стоимость в час:\n{pCInfo1._PC_Cost}р";
            if (pCInfo1._PC_Lock == false)
            {
                labelStatusANDTime3.Text = $"Занят";
            }
            else if (pCInfo1._PC_Lock == true)
            {
                labelStatusANDTime3.Text = $"Свободен";
            }
            labelConf23.Text = $"Комплектующие:\n{pCInfo2._PC_Conf}";
            labelCost23.Text = $"Стоимость в час:\n{pCInfo2._PC_Cost}р";
            if (pCInfo2._PC_Lock == false)
            {
                labelStatusANDTime23.Text = $"Занят";
            }
            else if (pCInfo2._PC_Lock == true)
            {
                labelStatusANDTime23.Text = $"Свободен";
            }
        }

        private void comboBox23_TextChanged(object sender, EventArgs e)
        {
            if (comboBox23.Text == "1 час")
            {
                labelFinalCost23.Text = $"Итоговая стоимость: {pCInfo2._PC_Cost}";
                stonks2 = pCInfo2._PC_Cost;
                dt = dateTime.AddHours(1);
            }
            else if (comboBox23.Text == "2 часа")
            {
                labelFinalCost23.Text = $"Итоговая стоимость: {pCInfo2._PC_Cost * 2}";
                stonks2 = pCInfo2._PC_Cost * 2;
                dt = dateTime.AddHours(2);
            }
            else if (comboBox23.Text == "3 часа")
            {
                labelFinalCost23.Text = $"Итоговая стоимость: {pCInfo2._PC_Cost * 3}";
                stonks2 = pCInfo2._PC_Cost * 3;
                dt = dateTime.AddHours(3);
            }
            labelFinalCost23.Visible = true;
            buttonForRent23.Visible = true;
        }

        private void buttonForRent13_Click(object sender, EventArgs e)
        {
            Rent rent = Rent.RentNow(_userInfo._userID, stonks1, pCInfo1._PC_ID, pCInfo1._PC_Lock, dt);
            this.Close();
        }

        private void comboBox13_TextChanged(object sender, EventArgs e)
        {
            if (comboBox13.Text == "1 час")
            {
                labelFinalCost13.Text = $"Итоговая стоимость: {pCInfo1._PC_Cost} р";
                stonks1 = pCInfo1._PC_Cost;
                dt = dateTime.AddHours(1);
            }
            else if (comboBox13.Text == "2 часа")
            {
                labelFinalCost13.Text = $"Итоговая стоимость: {pCInfo1._PC_Cost * 2} р";
                stonks1 = pCInfo1._PC_Cost * 2;
                dt = dateTime.AddHours(2);
            }
            else if (comboBox13.Text == "3 часа")
            {
                labelFinalCost13.Text = $"Итоговая стоимость: {pCInfo1._PC_Cost * 3} р";
                stonks1 = pCInfo1._PC_Cost * 3;
                dt = dateTime.AddHours(3);
            }
            labelFinalCost13.Visible = true;
            buttonForRent13.Visible = true;
        }

        private void buttonForRent23_Click(object sender, EventArgs e)
        {
            Rent rent = Rent.RentNow(_userInfo._userID, stonks2, pCInfo2._PC_ID, pCInfo2._PC_Lock, dt);
            this.Close();
        }
    }
}
