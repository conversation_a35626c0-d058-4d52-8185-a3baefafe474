﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Data.SqlClient;

namespace PC_cafe
{
    public partial class RegForm : Form
    {
        HashPass HashPass = new HashPass();
        public RegForm()
        {
            InitializeComponent();
            StartPosition = FormStartPosition.CenterParent;
            regLogBox.Text = "Введите логин";
            regLogBox.ForeColor = Color.Gray;
            regPassBox.Text = "Введите пароль";
            regPassBox.ForeColor = Color.Gray;
            regPassBox.UseSystemPasswordChar = false;
            FnameLabel.Text = "Введите имя";
            FnameLabel.ForeColor = Color.Gray;
            SnameLabel.Text = "Введите фамилию";
            SnameLabel.ForeColor = Color.Gray;
            TnameLabel.Text = "Введите отчество";
            TnameLabel.ForeColor = Color.Gray;
        }

        private void regButton_Click(object sender, EventArgs e)
        {
            if (regLogBox.Text == "Введите логин")
            {
                regLogBox.ForeColor = Color.Red;
                return;
            }

            if (regPassBox.Text == "Введите пароль")
            {
                regPassBox.ForeColor = Color.Red;
                return;
            }

            if (SnameLabel.Text == "Введите фамилию")
            {
                SnameLabel.ForeColor = Color.Red;
                return;
            }

            if (FnameLabel.Text == "Введите имя")
            {
                FnameLabel.ForeColor = Color.Red;
                return;
            }

            if (TnameLabel.Text == "Введите отчество" || TnameLabel.Text == "")
            {
                TnameLabel.Text = "";
            }

            DataBase dataBase = new DataBase();
            DataTable table = new DataTable();
            SqlDataAdapter adapter = new SqlDataAdapter();

            SqlCommand checkUserLogin = new SqlCommand("SELECT * FROM Пользователь WHERE Логин = @newUserL", dataBase.GetConnection());
            checkUserLogin.Parameters.Add("@newUserL", SqlDbType.VarChar).Value = regLogBox.Text;
            adapter.SelectCommand = checkUserLogin;
            adapter.Fill(table);
            if (table.Rows.Count > 0)
            {
                regErrorText.Visible = true;
                return;
            }
            var secretPass = HashPass.GetHashPass(regPassBox.Text);
            SqlCommand command = new SqlCommand("INSERT INTO Пользователь (Логин, Пароль, Фамилия, Имя, Отчество) VALUES (@newUserL, @newUserP, @newUserFname, @newUserSname, @newUserTname)", dataBase.GetConnection());
            command.Parameters.Add("@newUserL", SqlDbType.VarChar).Value = regLogBox.Text;
            command.Parameters.Add("@newUserP", SqlDbType.VarChar).Value = secretPass;
            command.Parameters.Add("@newUserFname", SqlDbType.VarChar).Value = FnameLabel.Text;
            command.Parameters.Add("@newUserSname", SqlDbType.VarChar).Value = SnameLabel.Text;
            if (TnameLabel.Text == "Введите отчество")
            {
                command.Parameters.Add("@newUserTname", SqlDbType.VarChar).Value = "NULL";
            }
            else
            {
                command.Parameters.Add("@newUserTname", SqlDbType.VarChar).Value = TnameLabel.Text;
            }
            dataBase.OpenConnection();

            if (command.ExecuteNonQuery() == 1)
            {
                MessageBox.Show("Регистрация успешна!");
                this.Close();
            }
            else
            {
                MessageBox.Show("Ошибка регистрации!");
                Application.Exit();
            }
            dataBase.CloseConnection();

        }

        private void regLogBox_Enter(object sender, EventArgs e)
        {
            if (regLogBox.Text == "Введите логин")
                regLogBox.Text = "";
            regLogBox.ForeColor = Color.Black;
        }

        private void regLogBox_Leave(object sender, EventArgs e)
        {
            if (regLogBox.Text == "")
            {
                regLogBox.Text = "Введите логин";
                regLogBox.ForeColor = Color.Gray;
            }
        }

        private void regPassBox_Enter(object sender, EventArgs e)
        {
            if (regPassBox.Text == "Введите пароль")
                regPassBox.Text = "";
            regPassBox.UseSystemPasswordChar = true;
            regPassBox.ForeColor = Color.Black;
        }

        private void regPassBox_Leave(object sender, EventArgs e)
        {
            if (regPassBox.Text == "")
            {
                regPassBox.Text = "Введите пароль";
                regPassBox.UseSystemPasswordChar = false;
                regPassBox.ForeColor = Color.Gray;
            }
        }

        private void SnameLabel_Enter(object sender, EventArgs e)
        {
            if (SnameLabel.Text == "Введите фамилию")
                SnameLabel.Text = "";
            SnameLabel.ForeColor = Color.Black;
        }

        private void SnameLabel_Leave(object sender, EventArgs e)
        {
            if (SnameLabel.Text == "")
            {
                SnameLabel.Text = "Введите фамилию";
                SnameLabel.ForeColor = Color.Gray;
            }
        }

        private void FnameLabel_Enter(object sender, EventArgs e)
        {
            if (FnameLabel.Text == "Введите имя")
                FnameLabel.Text = "";
            FnameLabel.ForeColor = Color.Black;
        }

        private void FnameLabel_Leave(object sender, EventArgs e)
        {
            if (FnameLabel.Text == "")
            {
                FnameLabel.Text = "Введите имя";
                FnameLabel.ForeColor = Color.Gray;
            }
        }

        private void TnameLabel_Enter(object sender, EventArgs e)
        {
            if (TnameLabel.Text == "Введите отчество")
                TnameLabel.Text = "";
            TnameLabel.ForeColor = Color.Black;
        }

        private void TnameLabel_Leave(object sender, EventArgs e)
        {
            if (TnameLabel.Text == "")
            {
                TnameLabel.Text = "Введите отчество";
                TnameLabel.ForeColor = Color.Gray;
            }
        }

        private void RegForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            LogForm logForm = new LogForm();
            logForm.Show();
        }
    }
}
