﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace PC_cafe
{
    public partial class LowForm : Form
    {
        DataBase dataBase = new DataBase();
        PCInfo pCInfo1 = PCInfo.GetPCInfo(1);
        PCInfo pCInfo2 = PCInfo.GetPCInfo(2);
        decimal stonks1 = 0;
        decimal stonks2 = 0;
        private readonly UserInfo _userInfo;
        DateTime dateTime = DateTime.Now;
        DateTime dt = new DateTime();
        
        public LowForm(UserInfo userInfo)
        {
            InitializeComponent();
            StartPosition = FormStartPosition.CenterScreen;
            _userInfo = userInfo;
        }

        private void LowForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            Main1 main1 = new Main1(_userInfo);
            main1.Show();
        }

        private void pictureBoxPC1_Click(object sender, EventArgs e)
        {
            pictureBoxPC1.Width = 164;
            pictureBoxPC1.Height = 164;
            pictureBoxPC2.Width = 385;
            pictureBoxPC2.Height = 385;

            labelConf1.Visible = true;
            labelCost1.Visible = true;
            labelStatusANDTime.Visible = true;
            if (labelStatusANDTime.Text == "Занят")
            {
                comboBox1.Visible = false;
                buttonForRent1.Visible = false;
            }
            else if (labelStatusANDTime.Text == "Свободен")
            {
                comboBox1.Visible = true;

                if (comboBox1.Text != "")
                {
                    buttonForRent1.Visible = true;
                    labelFinalCost1.Visible = true;
                }
                else
                {
                    buttonForRent1.Visible = false;
                    labelFinalCost1.Visible = false;
                }
                    
            }

            labelConf2.Visible = false;
            labelCost2.Visible = false;
            labelStatusANDTime2.Visible = false;
            comboBox2.Visible = false;
            buttonForRent2.Visible = false;
            labelFinalCost2.Visible = false;
        }

        private void pictureBoxPC2_Click(object sender, EventArgs e)
        {
            pictureBoxPC2.Width = 164;
            pictureBoxPC2.Height = 164;
            pictureBoxPC1.Width = 385;
            pictureBoxPC1.Height = 385;

            labelConf1.Visible = false;
            labelCost1.Visible = false;
            labelStatusANDTime.Visible = false;
            comboBox1.Visible = false;
            buttonForRent1.Visible = false;
            labelFinalCost1.Visible=false;

            labelConf2.Visible = true;
            labelCost2.Visible = true;
            labelStatusANDTime2.Visible = true;

            if (labelStatusANDTime2.Text == "Занят")
            {
                comboBox2.Visible = false;
                buttonForRent2.Visible = false;
            }
            else if (labelStatusANDTime2.Text == "Свободен")
            {
                comboBox2.Visible = true;
                if (comboBox2.Text == "")
                    buttonForRent2.Visible = false;
                else
                    buttonForRent2.Visible = true;
            }
        }

        private void LowForm_Load_1(object sender, EventArgs e)
        {
            labelConf1.Visible = false;
            labelCost1.Visible = false;
            labelStatusANDTime.Visible = false;
            comboBox1.Visible = false;
            buttonForRent1.Visible = false;
            labelFinalCost1.Visible = false;

            labelConf2.Visible = false;
            labelCost2.Visible = false;
            labelStatusANDTime2.Visible = false;
            comboBox2.Visible = false;
            buttonForRent2.Visible = false;
            labelFinalCost2.Visible = false;

            labelConf1.Text = $"Комплектующие:\n{pCInfo1._PC_Conf}";
            labelCost1.Text = $"Стоимость в час:\n{pCInfo1._PC_Cost}р";
            if (pCInfo1._PC_Lock == false)
            {
                labelStatusANDTime.Text = $"Занят";
            }
            else if (pCInfo1._PC_Lock == true)
            {
                labelStatusANDTime.Text = $"Свободен";
            }
            labelConf2.Text = $"Комплектующие:\n{pCInfo2._PC_Conf}";
            labelCost2.Text = $"Стоимость в час:\n{pCInfo2._PC_Cost}р";
            if (pCInfo2._PC_Lock == false)
            {
                labelStatusANDTime2.Text = $"Занят";
            }
            else if (pCInfo2._PC_Lock == true)
            {
                labelStatusANDTime2.Text = $"Свободен";
            }
        }

        private void comboBox2_TextChanged(object sender, EventArgs e)
        {
            if (comboBox2.Text == "1 час")
            {
                labelFinalCost2.Text = $"Итоговая стоимость: {pCInfo2._PC_Cost}";
                stonks2 = pCInfo2._PC_Cost;
                dt = dateTime.AddHours(1);
            }
            else if (comboBox2.Text == "2 часа")
            {
                labelFinalCost2.Text = $"Итоговая стоимость: {pCInfo2._PC_Cost * 2}";
                stonks2 = pCInfo2._PC_Cost * 2;
                dt = dateTime.AddHours(2);
            }
            else if (comboBox2.Text == "3 часа")
            {
                labelFinalCost2.Text = $"Итоговая стоимость: {pCInfo2._PC_Cost * 3}";
                stonks2 = pCInfo2._PC_Cost * 3;
                dt = dateTime.AddHours(3);
            }
            labelFinalCost2.Visible = true;
            buttonForRent2.Visible = true;
        }

        private void buttonForRent1_Click(object sender, EventArgs e)
        {
            Rent rent = Rent.RentNow(_userInfo._userID, stonks1, pCInfo1._PC_ID, pCInfo1._PC_Lock ,dt);
            this.Close();
        }

        private void comboBox1_TextChanged(object sender, EventArgs e)
        {
            if (comboBox1.Text == "1 час")
            {
                labelFinalCost1.Text = $"Итоговая стоимость: {pCInfo1._PC_Cost} р";
                stonks1 = pCInfo1._PC_Cost;
                dt = dateTime.AddHours(1);
            }
            else if (comboBox1.Text == "2 часа")
            {
                labelFinalCost1.Text = $"Итоговая стоимость: {pCInfo1._PC_Cost * 2} р";
                stonks1 = pCInfo1._PC_Cost * 2;
                dt = dateTime.AddHours(2);
            }
            else if (comboBox1.Text == "3 часа")
            {
                labelFinalCost1.Text = $"Итоговая стоимость: {pCInfo1._PC_Cost * 3} р";
                stonks1 = pCInfo1._PC_Cost * 3;
                dt = dateTime.AddHours(3);
            }
            labelFinalCost1.Visible = true;
            buttonForRent1.Visible = true;
        }

        private void buttonForRent2_Click(object sender, EventArgs e)
        {
            Rent rent = Rent.RentNow(_userInfo._userID, stonks2, pCInfo2._PC_ID, pCInfo2._PC_Lock, dt);
            this.Close();
        }
    }
}
