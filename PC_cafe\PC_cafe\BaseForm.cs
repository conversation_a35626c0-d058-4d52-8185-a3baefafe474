﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace PC_cafe
{
    public partial class BaseForm : Form
    {
        DataBase dataBase = new DataBase();
        PCInfo pCInfo1 = PCInfo.GetPCInfo(3);
        PCInfo pCInfo2 = PCInfo.GetPCInfo(4);
        decimal stonks1 = 0;
        decimal stonks2 = 0;
        private readonly UserInfo _userInfo;
        DateTime dateTime = DateTime.Now;
        DateTime dt = new DateTime();

        public BaseForm(UserInfo userInfo)
        {
            InitializeComponent();
            StartPosition = FormStartPosition.CenterScreen;
            _userInfo = userInfo;
        }

        private void BaseForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            Main1 main1 = new Main1(_userInfo);
            main1.Show();
        }

        private void pictureBoxPC12_Click(object sender, EventArgs e)
        {
            pictureBoxPC12.Width = 164;
            pictureBoxPC12.Height = 164;
            pictureBoxPC22.Width = 385;
            pictureBoxPC22.Height = 385;

            labelConf12.Visible = true;
            labelCost12.Visible = true;
            labelStatusANDTime2.Visible = true;
            if (labelStatusANDTime2.Text == "Занят")
            {
                comboBox12.Visible = false;
                buttonForRent12.Visible = false;
            }
            else if (labelStatusANDTime2.Text == "Свободен")
            {
                comboBox12.Visible = true;

                if (comboBox12.Text != "")
                {
                    buttonForRent12.Visible = true;
                    labelFinalCost12.Visible = true;
                }
                else
                {
                    buttonForRent12.Visible = false;
                    labelFinalCost12.Visible = false;
                }

            }

            labelConf22.Visible = false;
            labelCost22.Visible = false;
            labelStatusANDTime22.Visible = false;
            comboBox22.Visible = false;
            buttonForRent22.Visible = false;
            labelFinalCost22.Visible = false;
        }

        private void pictureBoxPC22_Click(object sender, EventArgs e)
        {
            pictureBoxPC22.Width = 164;
            pictureBoxPC22.Height = 164;
            pictureBoxPC12.Width = 385;
            pictureBoxPC12.Height = 385;

            labelConf12.Visible = false;
            labelCost12.Visible = false;
            labelStatusANDTime22.Visible = false;
            comboBox12.Visible = false;
            buttonForRent12.Visible = false;
            labelFinalCost12.Visible = false;

            labelConf22.Visible = true;
            labelCost22.Visible = true;
            labelStatusANDTime22.Visible = true;

            if (labelStatusANDTime22.Text == "Занят")
            {
                comboBox22.Visible = false;
                buttonForRent22.Visible = false;
            }
            else if (labelStatusANDTime22.Text == "Свободен")
            {
                comboBox22.Visible = true;
                if (comboBox22.Text == "")
                    buttonForRent22.Visible = false;
                else
                    buttonForRent22.Visible = true;
            }
        }

        private void BaseForm_Load_1(object sender, EventArgs e)
        {
            labelConf12.Visible = false;
            labelCost12.Visible = false;
            labelStatusANDTime2.Visible = false;
            comboBox12.Visible = false;
            buttonForRent12.Visible = false;
            labelFinalCost12.Visible = false;

            labelConf22.Visible = false;
            labelCost22.Visible = false;
            labelStatusANDTime22.Visible = false;
            comboBox22.Visible = false;
            buttonForRent22.Visible = false;
            labelFinalCost22.Visible = false;

            labelConf12.Text = $"Комплектующие:\n{pCInfo1._PC_Conf}";
            labelCost12.Text = $"Стоимость в час:\n{pCInfo1._PC_Cost}р";
            if (pCInfo1._PC_Lock == false)
            {
                labelStatusANDTime2.Text = $"Занят";
            }
            else if (pCInfo1._PC_Lock == true)
            {
                labelStatusANDTime2.Text = $"Свободен";
            }
            labelConf22.Text = $"Комплектующие:\n{pCInfo2._PC_Conf}";
            labelCost22.Text = $"Стоимость в час:\n{pCInfo2._PC_Cost}р";
            if (pCInfo2._PC_Lock == false)
            {
                labelStatusANDTime22.Text = $"Занят";
            }
            else if (pCInfo2._PC_Lock == true)
            {
                labelStatusANDTime22.Text = $"Свободен";
            }
        }

        private void comboBox22_TextChanged(object sender, EventArgs e)
        {
            if (comboBox22.Text == "1 час")
            {
                labelFinalCost22.Text = $"Итоговая стоимость: {pCInfo2._PC_Cost}";
                stonks2 = pCInfo2._PC_Cost;
                dt = dateTime.AddHours(1);
            }
            else if (comboBox22.Text == "2 часа")
            {
                labelFinalCost22.Text = $"Итоговая стоимость: {pCInfo2._PC_Cost * 2}";
                stonks2 = pCInfo2._PC_Cost * 2;
                dt = dateTime.AddHours(2);
            }
            else if (comboBox22.Text == "3 часа")
            {
                labelFinalCost22.Text = $"Итоговая стоимость: {pCInfo2._PC_Cost * 3}";
                stonks2 = pCInfo2._PC_Cost * 3;
                dt = dateTime.AddHours(3);
            }
            labelFinalCost22.Visible = true;
            buttonForRent22.Visible = true;
        }

        private void buttonForRent12_Click(object sender, EventArgs e)
        {
            Rent rent = Rent.RentNow(_userInfo._userID, stonks1, pCInfo1._PC_ID, pCInfo1._PC_Lock, dt);
            this.Close();
        }

        private void comboBox12_TextChanged(object sender, EventArgs e)
        {
            if (comboBox12.Text == "1 час")
            {
                labelFinalCost12.Text = $"Итоговая стоимость: {pCInfo1._PC_Cost} р";
                stonks1 = pCInfo1._PC_Cost;
                dt = dateTime.AddHours(1);
            }
            else if (comboBox12.Text == "2 часа")
            {
                labelFinalCost12.Text = $"Итоговая стоимость: {pCInfo1._PC_Cost * 2} р";
                stonks1 = pCInfo1._PC_Cost * 2;
                dt = dateTime.AddHours(2);
            }
            else if (comboBox12.Text == "3 часа")
            {
                labelFinalCost12.Text = $"Итоговая стоимость: {pCInfo1._PC_Cost * 3} р";
                stonks1 = pCInfo1._PC_Cost * 3;
                dt = dateTime.AddHours(3);
            }
            labelFinalCost12.Visible = true;
            buttonForRent12.Visible = true;
        }

        private void buttonForRent22_Click(object sender, EventArgs e)
        {
            Rent rent = Rent.RentNow(_userInfo._userID, stonks2, pCInfo2._PC_ID, pCInfo2._PC_Lock, dt);
            this.Close();
        }
    }
}
