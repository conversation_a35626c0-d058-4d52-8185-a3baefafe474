Министерство науки и высшего образования Российской Федерации 
Федеральное государственное автономное образовательное учреждение 
высшего образования 
ТОМСКИЙ ГОСУДАРСТВЕННЫЙ УНИВЕРСИТЕТ СИСТЕМ 
УПРАВЛЕНИЯ И РАДИОЭЛЕКТРОНИКИ (ТУСУР) 
Кафедра безопасности информационных систем (БИС) 
АИС С БД ДЛЯ КИНОТЕАТРА 
Руководство программиста  
«Принципы построения, проектирования и эксплуатации 
информационно-аналитических систем» 
Исполнитель 
Студент гр. 741-1 
__________ Черявко Е.Е. 
__________ 2025 г. 
Принял 
Преподаватель 
КИБЭВС 
кафедры 
__________ Ковалев И.В. 
__________ 2025 г. 
Томск 2025 
Перечень сокращений 
АИС – автоматизированная информационная система; 
БД – база данных; 
ОС – операционная система; 
СУБД – система управления базами данных. 
2  
1 Назначение и условия применения программы 
АИС «Кинотеатр» предназначена для ведения справочной информации 
и операций: фильмы, залы, сеансы, билеты, посетители, а также выполнения 
выборок и изменений в базе данных. 
1.1 Требования к техническим средствам 
Для работы «АИС с БД для кинотеатра» необходим один персональный 
компьютер с конфигурацией не ниже:  
1) ПК x64, 2+ ядра, 4 ГБ ОЗУ и более. 
2) Свободное место на диске от 200 МБ (без учёта БД). 
1.2 Требования к общему программному обеспечению (ОПО) 
Требования к общему программному обеспечению (ОПО), 
необходимому для «АИС с БД для кинотеатра», представлены в таблице 1.  
Таблица 1. ПК с приложением (клиент) 
Компонент 
ОС 
Требование 
Windows 10/11 x64 
.NET 
.NET Framework 4.7.2+ (Windows Forms, ADO.NET в составе) 
Конфигурация 
приложения 
Строка 
подключения CinemaConnectionString в App.config/Settings.settings (у
 казать сервер БД, каталог, режим аутентификации) 
Файл авторизации 
users.txt (UTF-8) в каталоге приложения: пары логин:пароль;  
Сеть (при удалённой БД) Доступ по TCP/IP к серверу БД (порт 1433/TCP; 1434/UDP для SQL 
Browser, если используется именованный инстанс) 
Права в БД 
Доступ на чтение/изменение таблиц предметной области и 
таблицы Лог 
Администрирование (оп
 ц.) 
SSMS для диагностики, при наличии прав 
3  
   
 
 4  
 
Таблица 2. ПК с сервером БД (SQL Server) 
Компонент Требование 
ОС Windows Server 2016/2019/2022 или Windows 10/11 x64 
СУБД Microsoft SQL Server 2016/2017/2019/2022 (Express/Standard/D
 eveloper) 
Сетевые протоколы Включён TCP/IP в SQL Server Configuration Manager 
Обнаружение инста
 нса 
Для именованного инстанса — служба SQL Server Browser = 
Running 
Брандмауэр 
Разрешить 1433/TCP (статический порт инстанса) 
и 1434/UDP (SQL Browser) либо настроенные порты, если 
нестандартные 
Аутентификация Windows Authentication (Integrated) 
и/или SQL Authentication (логин/пароль) 
База данных Развёрнута БД Cinema (схема таблиц Фильм/Посетитель/Зал/С
 еанс/Билет/Лог) 
Резервное копирова
 ние Настроены задания Backup/Restore по регламенту 
Мониторинг/аудит (
 опц.) 
Средства мониторинга SQL Server, аудит входов и 
изменений по политике ИБ 
 
Требования к периферийным устройствам, необходимому для «АИС с 
БД для кинотеатра», представлены в таблице 3.  
Таблица 3. Требования к периферийным устройствам 
Наименование Количество 
Клавиатура  1 
Мышь 1 
Система обеспечения бесперебойного питания 1 
 
 
 
  
2 Характеристика программы 
АИС «Кинотеатр» реализована как настольное приложение Windows 
Forms (.NET Framework 4.7.2+) с использованием типизированных наборов д
 анных (Typed DataSet) и TableAdapter’ов для доступа к Microsoft SQL Server 
по строке подключения CinemaConnectionString. 
Состав модулей: 
• Авторизация: ввод учётных данных, учёт неудачных попыток, 
блокировка входа на 5 минут после 3 ошибок, уведомление о времени до 
разблокировки. 
• Главное окно: навигация к разделам «Фильмы», «Билеты», 
«Посетители», «Залы», «Сеансы», «Запросы», а также «О программе» 
и «Выход». 
• Справочники: «Фильмы», «Посетители», «Залы», «Сеансы», 
«Билеты» — просмотр/редактирование записей, сохранение изменений в БД. 
• «Запросы»: примеры выборок (SELECT), полнотекстовый 
запрос по 
названию фильма, 
операции изменения 
данных (INSERT/UPDATE/DELETE) для сущности «Фильм». 
Модель данных: 
• Основные сущности: 
Фильм (ID_фильма), 
Посетитель 
(ID_посетителя), Зал (ID_зала), Сеанс (ID_сеанса), Билет (ID_Билета). 
• Связи: Сеанс связан с Фильмом и Залом; Билет связан с Сеансом и 
Посетителем. 
Безопасность и надёжность: 
• Механизм блокировки авторизации на 5 минут после 3 неуспешн
 ых попыток. 
• Логирование событий в таблицу «Лог» (INFO/WARN/ERROR): 
попытки и результаты входа, SQL-операции, необработанные исключения. 
5  
• Глобальные перехватчики исключений с уведомлением 
пользователя о критической ошибке. 
Конфигурация: 
• Подключение к БД задаётся параметром App.config/Settings.settin
 gs «CinemaConnectionString» (локальный SQL Server). 
• TableAdapter’ы привязаны к указанной строке подключения. 
Ограничения и предположения: 
• Локальное развертывание SQL Server; резервное копирование/вос
 становление выполняются штатными средствами СУБД. 
• Проверки данных базовые (наличие обязательных полей, коррект
 ность ID); детальные бизнес-правила не реализуются в приложении. 
6  
3 Обращение к программе 
3.1 Загрузка и запуск программы 
Для запуска «АИС с БД для кинотеатра» требуется установить нужные 
программы и зависимости.  
1) Установить СУБД SQL Server | Microsoft (Developer Edition) с 
официального сайта; 
2) Установить SQL Server Management Studio (SSMS) с официального 
сайта; 
3) Установить резервную копию базы данных в формате .bak со 
съемного носителя; 
4) Запустить SQL Server Management Studio (SSMS) и восстановить 
резервную копию базы данных; 
5) Установить .Net Framework 4.7.2 или выше c официального сайта; 
6) Установить Microsoft Visual studio 2022 Community Edition с 
официального сайта; 
7) Скачать и распаковать архив с программой «АИС с БД для 
кинотеатра» со съемного носителя; 
8) Установить корректную строку подключения в App.config и 
Settings.settings (имя: `CinemaConnectionString`). 
9) Запустить приложение (exe или из Visual Studio). 
3.2 Выполнение программы 
Описание работы «АИС с БД для кинотеатра» содержится в документе 
«Руководство администратора» 
7  
3.3 Завершение работы программы 
Для завершения работы «АИС с БД для кинотеатра» нужно нажать на 
красную кнопку «х» в правом верхнем углу окна или нажать на кнопку с 
иконкой двери, расположенной на верхней панели. 
8  
   
 
 9  
 
4 Входные и выходные данные  
 
Входными сигналами для системы являются команды взаимодействия с 
интерфейсом приложения АИС.  
Выходными сигналами являются отклики приложения на действия 
пользователей.  
Список входных и выходных сигналов представлен в таблице 3. 
 
Таблица 3. Список входных и выходных сигналов. 
Входной сигнал Выходной сигнал 
Окно «Авторизация» → поле «Логин»: ввод текста Значение используется 
при проверке в users.txt 
Окно «Авторизация» → поле «Пароль»: ввод текста Значение используется при 
проверке в users.txt 
Окно «Авторизация» → кнопка «Войти» при активной 
блокировке 
Сообщение: «Аккаунт 
заблокирован. Подождите {mins} 
мин.»; вход запрещён 
Окно «Авторизация» → кнопка «Войти» с корректными да
 нными (найдены в users.txt) 
Успешный вход: фиксация 
пользователя, сброс блокировки, 
запись в лог, открытие 
главного окна 
Окно «Авторизация» → кнопка «Войти» с неверными данн
 ыми 
Сообщение: «Неверный логин 
и/или пароль»; запись в лог; учёт 
неудачи и возможная 
блокировка на 5 минут 
Обращение к файлу users.txt (формат: login:password; 
одна пара на строку; разделители : ; , \t; 
строки с # игнорируются) 
При совпадении пары 
логин/пароль — допуск; при 
отсутствии — отказ в доступе 
Главное окно → «Файл → О программе»/кнопка «О 
программе» 
Сообщение с информацией о прог
 рамме 
Главное окно → «Файл → Выход»/кнопка «Выход» Закрытие приложения 
Главное окно 
→ «Фильмы»/«Билеты»/«Посетители»/«Залы»/«Сеансы»/«
 Запросы» 
Открытие соответствующих окон 
   
 
 10  
 
Продолжение таблицы 3. Список входных и выходных сигналов. 
Запросы → «Примеры запросов» → «Фильмы» Вывод всех фильмов 
Запросы → «Примеры запросов» → «Фильм и год» Вывод ID, названия и года 
Запросы → «Примеры запросов» → «Дата 
и время Фильма» 
Вывод фильмов и 
дат/времени сеансов 
Запросы → «Полная запись SELECT»: ввести «Название 
фильма» → «Выполнить» 
Результаты запроса; 
если поле пусто 
— «Обязательно заполните 
поле»; если нет строк — 
«Нет значений!» 
Запросы → «Запросы изменения данных» → «Показать 
список фильмов» Вывод всех фильмов 
Запросы → «Добавить данные фильма» → 
«Выполнить запрос» 
Добавление записи; 
при ошибке — сообщение 
и запись в лог 
Запросы → «Обновить 
данные фильма» → «Выполнить запрос» 
Обновление по ID; при 
отсутствии ID — 
сообщение 
Запросы → «Удалить фильм» → «Выполнить запрос» 
Удаление по ID; 
при некорректном ID — 
сообщение; при 
ошибке — «Ошибка удале
 ния» 
Запросы → включить «Удалить фильм» Скрытие панели ввода 
параметров фильма 
Залы → «Поиск»/«Фильтр» 
Поиск/фильтрация; при 
пустом критерии — «Вы 
ничего не задали»; при 
ошибках — «Ошибка 
поиска/фильтрации…»; 
при отсутствии — «Ничего 
не найдено»/«Нет таких» 
Сеансы → кнопка выбора фильма 
Диалог выбора; 
установка выбранного ID 
фильма 
Фильмы/Посетители/Залы/Сеансы/Билеты → «Сохранить 
данные» 
Сохранение в БД; «Билеты
 » — 
при нарушении ограничени
 й «Выход за ограничения» 
Приложение (глобально): необработанное исключение 
«Произошла 
непредвиденная ошибка. 
Подробности записаны в 
лог.»; запись в лог 
  
5 Сообщения 
5.1 Сообщения оператору  
1. 
Авторизация 
• При активной блокировке (после 3 неудачных попыток): «Аккаунт 
заблокирован. Подождите {mins} мин.» 
• При неверной паре логин/пароль (в users.txt нет соответствия): 
«Неверный логин и/или пароль» 
2. 
Главное окно 
• Меню «Файл → О программе» или кнопка «О программе»: «(C)Т
 УСУР,КИБЭВС,Черявко Егор Евгеньевич,741-1,2023г» 
3. 
Глобально (всё приложение) 
• Необработанное исключение (UI/другие потоки): «Произошла не
 предвиденная ошибка. Подробности записаны в лог.» 
4. 
Запросы 
• Вкладка «Полная запись SELECT» 
• Поле «Название фильма» пусто при нажатии «Выполнить»: «Обя
 зательно заполните поле» 
• Результат запроса пуст: «Нет значений!» 
• Исключение при выполнении запроса: «Ошибка выполнения запр
 оса.» + текст ошибки 
5. 
Вкладка «Запросы изменения данных» (Фильм) 
• INSERT: не заполнены обязательные поля: «Заполните все поля» 
• UPDATE: не указан ID: «Укажите ID фильма, для 
которого будете менять данные» 
• DELETE: ID не число: «Некоректное значение ID Фильма» 
• DELETE: исключение БД: «Ошибка удаления» + текст ошибки 
• Нажато «Выполнить запрос» без выбора действия: 
«Вы не выбрали действие» 
11  
6. 
Залы 
• Поиск: пустой критерий при «Поиск»: «Вы ничего не задали» 
• Поиск: исключение при выполнении: «Ошибка поиска …» 
• Поиск: запись не найдена: «Ничего не найдено» 
• Фильтр: включён при пустом критерии: «Вы ничего не задали» 
• Фильтр: исключение при применении: «Ошибка фильтрации …» 
• Фильтр: после применения записей нет (фильтр 
сбрасывается): «Нет таких» 
7. 
Билеты 
• Сохранение (Update) нарушает ограничения БД: «Выход за 
ограничения» 
8. 
Сеансы 
• Выбор фильма (вспомогательное): сообщение с числом 
выбранного ID фильма (отладочное) 
12  