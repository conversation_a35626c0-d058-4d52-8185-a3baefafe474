﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.SqlClient;
using System.Data;
using System.CodeDom;

namespace PC_cafe
{
    public class PCInfo
    {

        public int _PC_ID { get; set; }
        public string _PC_Conf { get; set; }
        public decimal _PC_Cost { get; set; }
        public bool _PC_Lock { get; set; }

        public PCInfo(int PC_ID, string PC_Conf, decimal PC_Cost, bool PC_Lock)
        {
            _PC_ID = PC_ID;
            _PC_Conf = PC_Conf;
            _PC_Cost = PC_Cost;
            _PC_Lock = PC_Lock;
        }

        public static PCInfo GetPCInfo(int PC_ID)
        {
            DataBase dataBase = new DataBase();
            SqlCommand command = new SqlCommand($"SELECT Комплектующие, Стоимость_в_час, Доступность FROM Место WHERE  ID_места = {PC_ID}", dataBase.GetConnection());

            dataBase.OpenConnection();
            using (SqlDataReader reader = command.ExecuteReader())
            {
                if (reader.Read())
                {
                    string PC_Conf = reader.GetString(0);
                    decimal PC_Cost = reader.GetDecimal(1);
                    bool PC_Lock = reader.GetBoolean(2);

                    dataBase.CloseConnection();

                    return new PCInfo(PC_ID, PC_Conf, PC_Cost, PC_Lock);
                }
            }
            throw new Exception("Произошла ошибка, попробуйте перезагрузить приложение");
        }
    }
}
