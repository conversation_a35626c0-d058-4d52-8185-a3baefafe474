﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Data.SqlClient;

namespace PC_cafe
{
    public partial class LogForm : Form
    {
        HashPass HashPass = new HashPass();
        public LogForm()
        {
            InitializeComponent();
            StartPosition = FormStartPosition.CenterScreen;
            logLogBox.Text = "Введите логин";
            logLogBox.ForeColor = Color.Gray;
            logPassBox.Text = "Введите пароль";
            logPassBox.ForeColor = Color.Gray;
        }

        private void logButton_Click(object sender, EventArgs e)
        {
            if (logLogBox.Text == "Введите логин")
            {
                logLogBox.ForeColor = Color.Red;
                return;
            }

            if (logPassBox.Text == "Введите пароль")
            {
                logPassBox.ForeColor = Color.Red;
                return;
            }
            string userLogin = logLogBox.Text;
            string userPassword = HashPass.GetHashPass(logPassBox.Text);
            DataBase dataBase = new DataBase();
            DataTable table = new DataTable();
            SqlDataAdapter adapter = new SqlDataAdapter();

            SqlCommand command = new SqlCommand($"SELECT ID_пользователя, Фамилия, Имя, Отчество, Логин, Пароль FROM Пользователь WHERE Логин = @userL AND Пароль = '{userPassword}'", dataBase.GetConnection());
            command.Parameters.Add("@userL", SqlDbType.VarChar).Value = userLogin;

            adapter.SelectCommand = command;
            adapter.Fill(table);

            if (table.Rows.Count == 0)
            {
                logErrorText.ForeColor = Color.Red;
                logErrorText.Visible = true;
            }
            else
            {
                var userInfo = new UserInfo(Convert.ToInt32(table.Rows[0].ItemArray[0]), table.Rows[0].ItemArray[1].ToString(), table.Rows[0].ItemArray[2].ToString(), table.Rows[0].ItemArray[3].ToString(), table.Rows[0].ItemArray[4].ToString());
                this.Hide();
                Main1 main1 = new Main1(userInfo);
                main1.Show();
            }
        }

        private void logLogBox_Enter(object sender, EventArgs e)
        {
            if (logLogBox.Text == "Введите логин")
                logLogBox.Text = "";
            logLogBox.ForeColor = Color.Black;
        }

        private void logLogBox_Leave(object sender, EventArgs e)
        {
            if (logLogBox.Text == "")
            {
                logLogBox.Text = "Введите логин";
                logLogBox.ForeColor = Color.Gray;
            }
        }

        private void logPassBox_Enter(object sender, EventArgs e)
        {
            if (logPassBox.Text == "Введите пароль")
                logPassBox.Text = "";
            logPassBox.UseSystemPasswordChar = true;
            logPassBox.ForeColor = Color.Black;
        }

        private void logPassBox_Leave(object sender, EventArgs e)
        {
            if (logPassBox.Text == "")
            {
                logPassBox.Text = "Введите пароль";
                logPassBox.UseSystemPasswordChar = false;
                logPassBox.ForeColor = Color.Gray;
            }
        }

        private void goRegButton_Click(object sender, EventArgs e)
        {
            RegForm regForm = new RegForm();
            this.Hide();
            regForm.ShowDialog();
        }

        private void logForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            Application.Exit();
        }
    }
}