﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="_PC_cafeDataSet" targetNamespace="http://tempuri.org/_PC_cafeDataSet.xsd" xmlns:mstns="http://tempuri.org/_PC_cafeDataSet.xsd" xmlns="http://tempuri.org/_PC_cafeDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="Settings" AppSettingsPropertyName="PC_cafeConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="PC_cafeConnectionString (Settings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.PC_cafe.Properties.Settings.GlobalReference.Default.PC_cafeConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="АрендаTableAdapter" GeneratorDataComponentClassName="АрендаTableAdapter" Name="Аренда" UserDataComponentName="АрендаTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="PC_cafeConnectionString (Settings)" DbObjectName="[PC-cafe].dbo.Аренда" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Аренда] WHERE (([ID_аренды_(PK)] = @p2) AND ([ID_пользователя_(FK)] = @p4) AND ([Прибыль] = @Original_Прибыль) AND ([Оплачено] = @Original_Оплачено) AND ([Время_начала] = @Original_Время_начала) AND ([Дата_начала] = @Original_Дата_начала))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_аренды_(PK)" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p4" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_пользователя_(FK)" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Original_Прибыль" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Прибыль" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Оплачено" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Оплачено" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Время_начала" Precision="0" ProviderType="SmallDateTime" Scale="0" Size="0" SourceColumn="Время_начала" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Дата_начала" Precision="0" ProviderType="SmallDateTime" Scale="0" Size="0" SourceColumn="Дата_начала" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Аренда] ([ID_аренды_(PK)], [Прибыль], [Оплачено], [Время_начала], [Дата_начала]) VALUES (@p1, @Прибыль, @Оплачено, @Время_начала, @Дата_начала);
SELECT [ID_аренды_(PK)], [ID_пользователя_(FK)], Прибыль, Оплачено, Время_начала, Дата_начала FROM Аренда WHERE ([ID_аренды_(PK)] = @ID_аренды__PK_)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_аренды_(PK)" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Прибыль" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Прибыль" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Оплачено" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Оплачено" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Время_начала" Precision="0" ProviderType="SmallDateTime" Scale="0" Size="0" SourceColumn="Время_начала" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Дата_начала" Precision="0" ProviderType="SmallDateTime" Scale="0" Size="0" SourceColumn="Дата_начала" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID_аренды__PK_" ColumnName="ID_аренды_(PK)" DataSourceName="[PC-cafe].dbo.Аренда" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID_аренды__PK_" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID_аренды_(PK)" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT [ID_аренды_(PK)], [ID_пользователя_(FK)], Прибыль, Оплачено, Время_начала, Дата_начала FROM dbo.Аренда</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Аренда] SET [ID_аренды_(PK)] = @p1, [Прибыль] = @Прибыль, [Оплачено] = @Оплачено, [Время_начала] = @Время_начала, [Дата_начала] = @Дата_начала WHERE (([ID_аренды_(PK)] = @p2) AND ([ID_пользователя_(FK)] = @p4) AND ([Прибыль] = @Original_Прибыль) AND ([Оплачено] = @Original_Оплачено) AND ([Время_начала] = @Original_Время_начала) AND ([Дата_начала] = @Original_Дата_начала));
SELECT [ID_аренды_(PK)], [ID_пользователя_(FK)], Прибыль, Оплачено, Время_начала, Дата_начала FROM Аренда WHERE ([ID_аренды_(PK)] = @ID_аренды__PK_)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_аренды_(PK)" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Прибыль" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Прибыль" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Оплачено" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Оплачено" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Время_начала" Precision="0" ProviderType="SmallDateTime" Scale="0" Size="0" SourceColumn="Время_начала" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Дата_начала" Precision="0" ProviderType="SmallDateTime" Scale="0" Size="0" SourceColumn="Дата_начала" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_аренды_(PK)" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p4" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_пользователя_(FK)" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Original_Прибыль" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Прибыль" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Оплачено" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Оплачено" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Время_начала" Precision="0" ProviderType="SmallDateTime" Scale="0" Size="0" SourceColumn="Время_начала" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Дата_начала" Precision="0" ProviderType="SmallDateTime" Scale="0" Size="0" SourceColumn="Дата_начала" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID_аренды__PK_" ColumnName="ID_аренды_(PK)" DataSourceName="[PC-cafe].dbo.Аренда" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID_аренды__PK_" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID_аренды_(PK)" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID_аренды_(PK)" DataSetColumn="ID_аренды_(PK)" />
              <Mapping SourceColumn="ID_пользователя_(FK)" DataSetColumn="ID_пользователя_(FK)" />
              <Mapping SourceColumn="Прибыль" DataSetColumn="Прибыль" />
              <Mapping SourceColumn="Оплачено" DataSetColumn="Оплачено" />
              <Mapping SourceColumn="Время_начала" DataSetColumn="Время_начала" />
              <Mapping SourceColumn="Дата_начала" DataSetColumn="Дата_начала" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Аренда_местаTableAdapter" GeneratorDataComponentClassName="Аренда_местаTableAdapter" Name="Аренда_места" UserDataComponentName="Аренда_местаTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="PC_cafeConnectionString (Settings)" DbObjectName="[PC-cafe].dbo.Аренда_места" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Аренда_места] ([ID_аренды_(FK)], [Время_окончания], [Дата_окончания], [Прибыль]) VALUES (@p1, @Время_окончания, @Дата_окончания, @Прибыль)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p1" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_аренды_(FK)" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Время_окончания" Precision="0" ProviderType="SmallDateTime" Scale="0" Size="0" SourceColumn="Время_окончания" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Дата_окончания" Precision="0" ProviderType="SmallDateTime" Scale="0" Size="0" SourceColumn="Дата_окончания" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Прибыль" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Прибыль" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT [ID_аренды_(FK)], [ID_места_(FK)], Время_окончания, Дата_окончания, Прибыль FROM dbo.Аренда_места</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID_аренды_(FK)" DataSetColumn="ID_аренды_(FK)" />
              <Mapping SourceColumn="ID_места_(FK)" DataSetColumn="ID_места_(FK)" />
              <Mapping SourceColumn="Время_окончания" DataSetColumn="Время_окончания" />
              <Mapping SourceColumn="Дата_окончания" DataSetColumn="Дата_окончания" />
              <Mapping SourceColumn="Прибыль" DataSetColumn="Прибыль" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ЗалTableAdapter" GeneratorDataComponentClassName="ЗалTableAdapter" Name="Зал" UserDataComponentName="ЗалTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="PC_cafeConnectionString (Settings)" DbObjectName="[PC-cafe].dbo.Зал" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Зал] WHERE (([ID_зала_(PK)] = @p2) AND ([Наименование] = @Original_Наименование))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_зала_(PK)" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Наименование" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Наименование" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Зал] ([Наименование]) VALUES (@Наименование);
SELECT [ID_зала_(PK)], Наименование FROM Зал WHERE ([ID_зала_(PK)] = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Наименование" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Наименование" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT [ID_зала_(PK)], Наименование FROM dbo.Зал</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Зал] SET [Наименование] = @Наименование WHERE (([ID_зала_(PK)] = @p2) AND ([Наименование] = @Original_Наименование));
SELECT [ID_зала_(PK)], Наименование FROM Зал WHERE ([ID_зала_(PK)] = @ID_зала__PK_)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Наименование" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Наименование" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_зала_(PK)" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Наименование" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Наименование" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID_зала__PK_" ColumnName="ID_зала_(PK)" DataSourceName="[PC-cafe].dbo.Зал" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID_зала__PK_" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID_зала_(PK)" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID_зала_(PK)" DataSetColumn="ID_зала_(PK)" />
              <Mapping SourceColumn="Наименование" DataSetColumn="Наименование" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="МестоTableAdapter" GeneratorDataComponentClassName="МестоTableAdapter" Name="Место" UserDataComponentName="МестоTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="PC_cafeConnectionString (Settings)" DbObjectName="[PC-cafe].dbo.Место" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Место] WHERE (([ID_места_(PK)] = @p2) AND ((@IsNull_Комплектующие = 1 AND [Комплектующие] IS NULL) OR ([Комплектующие] = @Original_Комплектующие)) AND ([ID_зала_(FK)] = @p4) AND ([Стоимость_в_час] = @Original_Стоимость_в_час) AND ([Доступность] = @Original_Доступность))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_места_(PK)" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Комплектующие" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Комплектующие" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Комплектующие" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Комплектующие" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p4" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_зала_(FK)" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Original_Стоимость_в_час" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Стоимость_в_час" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Доступность" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Доступность" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Место] ([Комплектующие], [ID_зала_(FK)], [Стоимость_в_час], [Доступность]) VALUES (@Комплектующие, @p3, @Стоимость_в_час, @Доступность);
SELECT [ID_места_(PK)], Комплектующие, [ID_зала_(FK)], Стоимость_в_час, Доступность FROM Место WHERE ([ID_места_(PK)] = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Комплектующие" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Комплектующие" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p3" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_зала_(FK)" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Стоимость_в_час" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Стоимость_в_час" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Доступность" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Доступность" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT [ID_места_(PK)], Комплектующие, [ID_зала_(FK)], Стоимость_в_час, Доступность FROM dbo.Место</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Место] SET [Комплектующие] = @Комплектующие, [ID_зала_(FK)] = @p3, [Стоимость_в_час] = @Стоимость_в_час, [Доступность] = @Доступность WHERE (([ID_места_(PK)] = @p2) AND ((@IsNull_Комплектующие = 1 AND [Комплектующие] IS NULL) OR ([Комплектующие] = @Original_Комплектующие)) AND ([ID_зала_(FK)] = @p4) AND ([Стоимость_в_час] = @Original_Стоимость_в_час) AND ([Доступность] = @Original_Доступность));
SELECT [ID_места_(PK)], Комплектующие, [ID_зала_(FK)], Стоимость_в_час, Доступность FROM Место WHERE ([ID_места_(PK)] = @ID_места__PK_)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Комплектующие" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Комплектующие" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p3" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_зала_(FK)" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Стоимость_в_час" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Стоимость_в_час" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Доступность" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Доступность" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p2" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_места_(PK)" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Комплектующие" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Комплектующие" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Комплектующие" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Комплектующие" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@p4" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_зала_(FK)" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Original_Стоимость_в_час" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Стоимость_в_час" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Доступность" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Доступность" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID_места__PK_" ColumnName="ID_места_(PK)" DataSourceName="[PC-cafe].dbo.Место" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID_места__PK_" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID_места_(PK)" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID_места_(PK)" DataSetColumn="ID_места_(PK)" />
              <Mapping SourceColumn="Комплектующие" DataSetColumn="Комплектующие" />
              <Mapping SourceColumn="ID_зала_(FK)" DataSetColumn="ID_зала_(FK)" />
              <Mapping SourceColumn="Стоимость_в_час" DataSetColumn="Стоимость_в_час" />
              <Mapping SourceColumn="Доступность" DataSetColumn="Доступность" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ПользовательTableAdapter" GeneratorDataComponentClassName="ПользовательTableAdapter" Name="Пользователь" UserDataComponentName="ПользовательTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="PC_cafeConnectionString (Settings)" DbObjectName="[PC-cafe].dbo.Пользователь" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Пользователь] WHERE (([ID_пользователя] = @Original_ID_пользователя) AND ([Фамилия] = @Original_Фамилия) AND ([Имя] = @Original_Имя) AND ((@IsNull_Отчество = 1 AND [Отчество] IS NULL) OR ([Отчество] = @Original_Отчество)) AND ([Логин] = @Original_Логин) AND ([Пароль] = @Original_Пароль))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID_пользователя" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_пользователя" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Фамилия" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Фамилия" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Имя" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Имя" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Отчество" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Отчество" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Отчество" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Отчество" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Логин" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Логин" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Пароль" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Пароль" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Пользователь] ([Фамилия], [Имя], [Отчество], [Логин], [Пароль]) VALUES (@Фамилия, @Имя, @Отчество, @Логин, @Пароль);
SELECT ID_пользователя, Фамилия, Имя, Отчество, Логин, Пароль FROM Пользователь WHERE (ID_пользователя = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Фамилия" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Фамилия" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Имя" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Имя" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Отчество" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Отчество" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Логин" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Логин" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Пароль" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Пароль" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ID_пользователя, Фамилия, Имя, Отчество, Логин, Пароль FROM dbo.Пользователь</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Пользователь] SET [Фамилия] = @Фамилия, [Имя] = @Имя, [Отчество] = @Отчество, [Логин] = @Логин, [Пароль] = @Пароль WHERE (([ID_пользователя] = @Original_ID_пользователя) AND ([Фамилия] = @Original_Фамилия) AND ([Имя] = @Original_Имя) AND ((@IsNull_Отчество = 1 AND [Отчество] IS NULL) OR ([Отчество] = @Original_Отчество)) AND ([Логин] = @Original_Логин) AND ([Пароль] = @Original_Пароль));
SELECT ID_пользователя, Фамилия, Имя, Отчество, Логин, Пароль FROM Пользователь WHERE (ID_пользователя = @ID_пользователя)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Фамилия" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Фамилия" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Имя" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Имя" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Отчество" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Отчество" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Логин" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Логин" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Пароль" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Пароль" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID_пользователя" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID_пользователя" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Фамилия" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Фамилия" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Имя" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Имя" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Отчество" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Отчество" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Отчество" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Отчество" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Логин" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Логин" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Пароль" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Пароль" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID_пользователя" ColumnName="ID_пользователя" DataSourceName="[PC-cafe].dbo.Пользователь" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID_пользователя" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID_пользователя" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID_пользователя" DataSetColumn="ID_пользователя" />
              <Mapping SourceColumn="Фамилия" DataSetColumn="Фамилия" />
              <Mapping SourceColumn="Имя" DataSetColumn="Имя" />
              <Mapping SourceColumn="Отчество" DataSetColumn="Отчество" />
              <Mapping SourceColumn="Логин" DataSetColumn="Логин" />
              <Mapping SourceColumn="Пароль" DataSetColumn="Пароль" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="_PC_cafeDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="True" msprop:Generator_UserDSName="_PC_cafeDataSet" msprop:Generator_DataSetName="_PC_cafeDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Аренда" msprop:Generator_RowEvHandlerName="АрендаRowChangeEventHandler" msprop:Generator_RowDeletedName="АрендаRowDeleted" msprop:Generator_RowDeletingName="АрендаRowDeleting" msprop:Generator_RowEvArgName="АрендаRowChangeEvent" msprop:Generator_TablePropName="Аренда" msprop:Generator_RowChangedName="АрендаRowChanged" msprop:Generator_RowChangingName="АрендаRowChanging" msprop:Generator_TableClassName="АрендаDataTable" msprop:Generator_RowClassName="АрендаRow" msprop:Generator_TableVarName="tableАренда" msprop:Generator_UserTableName="Аренда">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID_аренды__x0028_PK_x0029_" msprop:Generator_ColumnPropNameInRow="_ID_аренды__PK_" msprop:Generator_ColumnPropNameInTable="_ID_аренды__PK_Column" msprop:Generator_ColumnVarNameInTable="_columnID_аренды__PK_" msprop:Generator_UserColumnName="ID_аренды_(PK)" type="xs:int" />
              <xs:element name="ID_пользователя__x0028_FK_x0029_" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInRow="_ID_пользователя__FK_" msprop:Generator_ColumnPropNameInTable="_ID_пользователя__FK_Column" msprop:Generator_ColumnVarNameInTable="_columnID_пользователя__FK_" msprop:Generator_UserColumnName="ID_пользователя_(FK)" type="xs:int" />
              <xs:element name="Прибыль" msprop:Generator_ColumnPropNameInRow="Прибыль" msprop:Generator_ColumnPropNameInTable="ПрибыльColumn" msprop:Generator_ColumnVarNameInTable="columnПрибыль" msprop:Generator_UserColumnName="Прибыль" type="xs:decimal" />
              <xs:element name="Оплачено" msprop:Generator_ColumnPropNameInRow="Оплачено" msprop:Generator_ColumnPropNameInTable="ОплаченоColumn" msprop:Generator_ColumnVarNameInTable="columnОплачено" msprop:Generator_UserColumnName="Оплачено" type="xs:boolean" />
              <xs:element name="Время_начала" msprop:Generator_ColumnPropNameInRow="Время_начала" msprop:Generator_ColumnPropNameInTable="Время_началаColumn" msprop:Generator_ColumnVarNameInTable="columnВремя_начала" msprop:Generator_UserColumnName="Время_начала" type="xs:dateTime" />
              <xs:element name="Дата_начала" msprop:Generator_ColumnPropNameInRow="Дата_начала" msprop:Generator_ColumnPropNameInTable="Дата_началаColumn" msprop:Generator_ColumnVarNameInTable="columnДата_начала" msprop:Generator_UserColumnName="Дата_начала" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Аренда_места" msprop:Generator_RowEvHandlerName="Аренда_местаRowChangeEventHandler" msprop:Generator_RowDeletedName="Аренда_местаRowDeleted" msprop:Generator_RowDeletingName="Аренда_местаRowDeleting" msprop:Generator_RowEvArgName="Аренда_местаRowChangeEvent" msprop:Generator_TablePropName="Аренда_места" msprop:Generator_RowChangedName="Аренда_местаRowChanged" msprop:Generator_RowChangingName="Аренда_местаRowChanging" msprop:Generator_TableClassName="Аренда_местаDataTable" msprop:Generator_RowClassName="Аренда_местаRow" msprop:Generator_TableVarName="tableАренда_места" msprop:Generator_UserTableName="Аренда_места">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID_аренды__x0028_FK_x0029_" msprop:Generator_ColumnPropNameInRow="_ID_аренды__FK_" msprop:Generator_ColumnPropNameInTable="_ID_аренды__FK_Column" msprop:Generator_ColumnVarNameInTable="_columnID_аренды__FK_" msprop:Generator_UserColumnName="ID_аренды_(FK)" type="xs:int" />
              <xs:element name="ID_места__x0028_FK_x0029_" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInRow="_ID_места__FK_" msprop:Generator_ColumnPropNameInTable="_ID_места__FK_Column" msprop:Generator_ColumnVarNameInTable="_columnID_места__FK_" msprop:Generator_UserColumnName="ID_места_(FK)" type="xs:int" />
              <xs:element name="Время_окончания" msprop:Generator_ColumnPropNameInRow="Время_окончания" msprop:Generator_ColumnPropNameInTable="Время_окончанияColumn" msprop:Generator_ColumnVarNameInTable="columnВремя_окончания" msprop:Generator_UserColumnName="Время_окончания" type="xs:dateTime" />
              <xs:element name="Дата_окончания" msprop:Generator_ColumnPropNameInRow="Дата_окончания" msprop:Generator_ColumnPropNameInTable="Дата_окончанияColumn" msprop:Generator_ColumnVarNameInTable="columnДата_окончания" msprop:Generator_UserColumnName="Дата_окончания" type="xs:dateTime" />
              <xs:element name="Прибыль" msprop:Generator_ColumnPropNameInRow="Прибыль" msprop:Generator_ColumnPropNameInTable="ПрибыльColumn" msprop:Generator_ColumnVarNameInTable="columnПрибыль" msprop:Generator_UserColumnName="Прибыль" type="xs:decimal" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Зал" msprop:Generator_RowEvHandlerName="ЗалRowChangeEventHandler" msprop:Generator_RowDeletedName="ЗалRowDeleted" msprop:Generator_RowDeletingName="ЗалRowDeleting" msprop:Generator_RowEvArgName="ЗалRowChangeEvent" msprop:Generator_TablePropName="Зал" msprop:Generator_RowChangedName="ЗалRowChanged" msprop:Generator_RowChangingName="ЗалRowChanging" msprop:Generator_TableClassName="ЗалDataTable" msprop:Generator_RowClassName="ЗалRow" msprop:Generator_TableVarName="tableЗал" msprop:Generator_UserTableName="Зал">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID_зала__x0028_PK_x0029_" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInRow="_ID_зала__PK_" msprop:Generator_ColumnPropNameInTable="_ID_зала__PK_Column" msprop:Generator_ColumnVarNameInTable="_columnID_зала__PK_" msprop:Generator_UserColumnName="ID_зала_(PK)" type="xs:int" />
              <xs:element name="Наименование" msprop:Generator_ColumnPropNameInRow="Наименование" msprop:Generator_ColumnPropNameInTable="НаименованиеColumn" msprop:Generator_ColumnVarNameInTable="columnНаименование" msprop:Generator_UserColumnName="Наименование">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Место" msprop:Generator_RowEvHandlerName="МестоRowChangeEventHandler" msprop:Generator_RowDeletedName="МестоRowDeleted" msprop:Generator_RowDeletingName="МестоRowDeleting" msprop:Generator_RowEvArgName="МестоRowChangeEvent" msprop:Generator_TablePropName="Место" msprop:Generator_RowChangedName="МестоRowChanged" msprop:Generator_RowChangingName="МестоRowChanging" msprop:Generator_TableClassName="МестоDataTable" msprop:Generator_RowClassName="МестоRow" msprop:Generator_TableVarName="tableМесто" msprop:Generator_UserTableName="Место">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID_места__x0028_PK_x0029_" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInRow="_ID_места__PK_" msprop:Generator_ColumnPropNameInTable="_ID_места__PK_Column" msprop:Generator_ColumnVarNameInTable="_columnID_места__PK_" msprop:Generator_UserColumnName="ID_места_(PK)" type="xs:int" />
              <xs:element name="Комплектующие" msprop:Generator_ColumnPropNameInRow="Комплектующие" msprop:Generator_ColumnPropNameInTable="КомплектующиеColumn" msprop:Generator_ColumnVarNameInTable="columnКомплектующие" msprop:Generator_UserColumnName="Комплектующие" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ID_зала__x0028_FK_x0029_" msprop:Generator_ColumnPropNameInRow="_ID_зала__FK_" msprop:Generator_ColumnPropNameInTable="_ID_зала__FK_Column" msprop:Generator_ColumnVarNameInTable="_columnID_зала__FK_" msprop:Generator_UserColumnName="ID_зала_(FK)" type="xs:int" />
              <xs:element name="Стоимость_в_час" msprop:Generator_ColumnPropNameInRow="Стоимость_в_час" msprop:Generator_ColumnPropNameInTable="Стоимость_в_часColumn" msprop:Generator_ColumnVarNameInTable="columnСтоимость_в_час" msprop:Generator_UserColumnName="Стоимость_в_час" type="xs:decimal" />
              <xs:element name="Доступность" msprop:Generator_ColumnPropNameInRow="Доступность" msprop:Generator_ColumnPropNameInTable="ДоступностьColumn" msprop:Generator_ColumnVarNameInTable="columnДоступность" msprop:Generator_UserColumnName="Доступность" type="xs:boolean" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Пользователь" msprop:Generator_RowEvHandlerName="ПользовательRowChangeEventHandler" msprop:Generator_RowDeletedName="ПользовательRowDeleted" msprop:Generator_RowDeletingName="ПользовательRowDeleting" msprop:Generator_RowEvArgName="ПользовательRowChangeEvent" msprop:Generator_TablePropName="Пользователь" msprop:Generator_RowChangedName="ПользовательRowChanged" msprop:Generator_RowChangingName="ПользовательRowChanging" msprop:Generator_TableClassName="ПользовательDataTable" msprop:Generator_RowClassName="ПользовательRow" msprop:Generator_TableVarName="tableПользователь" msprop:Generator_UserTableName="Пользователь">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID_пользователя" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnPropNameInRow="ID_пользователя" msprop:Generator_ColumnPropNameInTable="ID_пользователяColumn" msprop:Generator_ColumnVarNameInTable="columnID_пользователя" msprop:Generator_UserColumnName="ID_пользователя" type="xs:int" />
              <xs:element name="Фамилия" msprop:Generator_ColumnPropNameInRow="Фамилия" msprop:Generator_ColumnPropNameInTable="ФамилияColumn" msprop:Generator_ColumnVarNameInTable="columnФамилия" msprop:Generator_UserColumnName="Фамилия">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Имя" msprop:Generator_ColumnPropNameInRow="Имя" msprop:Generator_ColumnPropNameInTable="ИмяColumn" msprop:Generator_ColumnVarNameInTable="columnИмя" msprop:Generator_UserColumnName="Имя">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Отчество" msprop:Generator_ColumnPropNameInRow="Отчество" msprop:Generator_ColumnPropNameInTable="ОтчествоColumn" msprop:Generator_ColumnVarNameInTable="columnОтчество" msprop:Generator_UserColumnName="Отчество" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Логин" msprop:Generator_ColumnPropNameInRow="Логин" msprop:Generator_ColumnPropNameInTable="ЛогинColumn" msprop:Generator_ColumnVarNameInTable="columnЛогин" msprop:Generator_UserColumnName="Логин">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Пароль" msprop:Generator_ColumnPropNameInRow="Пароль" msprop:Generator_ColumnPropNameInTable="ПарольColumn" msprop:Generator_ColumnVarNameInTable="columnПароль" msprop:Generator_UserColumnName="Пароль">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Аренда" />
      <xs:field xpath="mstns:ID_аренды__x0028_PK_x0029_" />
    </xs:unique>
    <xs:unique name="Зал_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Зал" />
      <xs:field xpath="mstns:ID_зала__x0028_PK_x0029_" />
    </xs:unique>
    <xs:unique name="Место_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Место" />
      <xs:field xpath="mstns:ID_места__x0028_PK_x0029_" />
    </xs:unique>
    <xs:unique name="Пользователь_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Пользователь" />
      <xs:field xpath="mstns:ID_пользователя" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="FK_Аренда_Пользователь" msdata:parent="Пользователь" msdata:child="Аренда" msdata:parentkey="ID_пользователя" msdata:childkey="ID_пользователя__x0028_FK_x0029_" msprop:Generator_UserParentTable="Пользователь" msprop:Generator_UserChildTable="Аренда" msprop:Generator_RelationVarName="relationFK_Аренда_Пользователь" msprop:Generator_UserRelationName="FK_Аренда_Пользователь" msprop:Generator_ChildPropName="GetАрендаRows" msprop:Generator_ParentPropName="ПользовательRow" />
      <msdata:Relationship name="FK_Аренда_x0020_места_Аренда" msdata:parent="Аренда" msdata:child="Аренда_места" msdata:parentkey="ID_аренды__x0028_PK_x0029_" msdata:childkey="ID_аренды__x0028_FK_x0029_" msprop:Generator_UserParentTable="Аренда" msprop:Generator_UserChildTable="Аренда_места" msprop:Generator_RelationVarName="relationFK_Аренда_места_Аренда" msprop:Generator_ChildPropName="GetАренда_местаRows" msprop:Generator_ParentPropName="АрендаRow" msprop:Generator_UserRelationName="FK_Аренда места_Аренда" />
      <msdata:Relationship name="FK_Аренда_x0020_места_Место" msdata:parent="Место" msdata:child="Аренда_места" msdata:parentkey="ID_места__x0028_PK_x0029_" msdata:childkey="ID_места__x0028_FK_x0029_" msprop:Generator_UserParentTable="Место" msprop:Generator_UserChildTable="Аренда_места" msprop:Generator_RelationVarName="relationFK_Аренда_места_Место" msprop:Generator_UserRelationName="FK_Аренда места_Место" msprop:Generator_ChildPropName="GetАренда_местаRows" msprop:Generator_ParentPropName="МестоRow" />
      <msdata:Relationship name="FK_Место_Зал" msdata:parent="Зал" msdata:child="Место" msdata:parentkey="ID_зала__x0028_PK_x0029_" msdata:childkey="ID_зала__x0028_FK_x0029_" msprop:Generator_UserParentTable="Зал" msprop:Generator_UserChildTable="Место" msprop:Generator_RelationVarName="relationFK_Место_Зал" msprop:Generator_ChildPropName="GetМестоRows" msprop:Generator_ParentPropName="ЗалRow" msprop:Generator_UserRelationName="FK_Место_Зал" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>