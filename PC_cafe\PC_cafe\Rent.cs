﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Drawing;

namespace PC_cafe
{
    internal class Rent
    {
        public int _Rent_ID { get; set; }
        public int _User_ID { get; set; }
        public int _PC_ID { get; set; }
        public decimal _Stonks { get; set; }
        public bool _PC_Lock { get; set; }
        public bool _IsPayed { get; set; }
        public DateTime _FDateTime { get; }
        public DateTime _EDateTime { get; set; }

        public Rent(int Rent_ID, int User_ID, int PC_ID, bool PC_Lock, decimal Stonks, bool IsPayed, DateTime EDateTime)
        {
            _Rent_ID = Rent_ID;
            _User_ID = User_ID;
            _PC_ID = PC_ID;
            _PC_Lock = PC_Lock;
            _Stonks = Stonks;
            _IsPayed = IsPayed;
            _FDateTime = DateTime.Now;
            _EDateTime = EDateTime;
        }

        public static Rent RentNow(int User_ID, decimal Stonks, int PC_ID, bool PC_Lock, DateTime dataTime)
        {
            int Rent_ID = 0;
            decimal TotalStonks = 0;
            bool IsPayed = false;
            decimal StonksF = Convert.ToDecimal((double)Stonks);

            DataBase dataBase = new DataBase();
            DataTable table = new DataTable();
            SqlDataAdapter adapter = new SqlDataAdapter();

            DateTime FDateTimeNow = DateTime.Now;
            DateTime FDateTime = FDateTimeNow;

            SqlCommand command = new SqlCommand("INSERT INTO Аренда (ID_пользователя, Прибыль, Время_начала) VALUES (@UserID, @Stonks, @FDateTime)", dataBase.GetConnection());
            command.Parameters.Add("@UserID", SqlDbType.VarChar).Value = User_ID;
            command.Parameters.Add("@Stonks", SqlDbType.VarChar).Value = StonksF;
            command.Parameters.Add("@FDateTime", SqlDbType.VarChar).Value = FDateTime;
            dataBase.OpenConnection();

            if (command.ExecuteNonQuery() == 1)
            {
                command = new SqlCommand("SELECT * FROM Аренда WHERE ID_пользователя = @UserID AND Прибыль = @Stonks AND Время_начала = @FDateTime", dataBase.GetConnection());
                command.Parameters.Add("@UserID", SqlDbType.VarChar).Value = User_ID;
                command.Parameters.Add("@Stonks", SqlDbType.VarChar).Value = StonksF;
                command.Parameters.Add("@FDateTime", SqlDbType.VarChar).Value = FDateTime;

                adapter.SelectCommand = command;
                adapter.Fill(table);

                if (table.Rows.Count == 0)
                {
                    command = new SqlCommand("DELETE FROM Аренда WHERE ID_пользователя = @UserID AND Прибыль = @Stonks AND Время_начала = @FDateTime", dataBase.GetConnection());
                    command.Parameters.Add("@UserID", SqlDbType.VarChar).Value = User_ID;
                    command.Parameters.Add("@Stonks", SqlDbType.VarChar).Value = StonksF;
                    command.Parameters.Add("@FDateTime", SqlDbType.VarChar).Value = FDateTime;
                    throw new Exception("Упс...");
                }

                else
                {
                    Rent_ID = Convert.ToInt32(table.Rows[0].ItemArray[0]);
                    TotalStonks = Convert.ToDecimal((double)StonksF * 0.9);
                    IsPayed = true;
                    DateTime dataTime1 = dataTime;
                    command = new SqlCommand("INSERT INTO Аренда_места (ID_аренды, ID_места, Время_окончания, Прибыль) VALUES (@Rent_ID, @PC_ID, @EDateTime, @TotalStonks)", dataBase.GetConnection());
                    command.Parameters.Add("@Rent_ID", SqlDbType.VarChar).Value = Rent_ID;
                    command.Parameters.Add("@PC_ID", SqlDbType.VarChar).Value = PC_ID;
                    command.Parameters.Add("@EDateTime", SqlDbType.VarChar).Value = dataTime1;
                    command.Parameters.Add("@TotalStonks", SqlDbType.VarChar).Value = TotalStonks;


                    if (command.ExecuteNonQuery() == 1)
                    {
                        command = new SqlCommand("DELETE FROM Аренда_места WHERE ID_аренды = @Rent_ID AND ID_места = @PC_ID AND Время_окончания = @EDateTime AND Прибыль = TotalStonks", dataBase.GetConnection());
                        command.Parameters.Add("@Rent_ID", SqlDbType.VarChar).Value = Rent_ID;
                        command.Parameters.Add("@PC_ID", SqlDbType.VarChar).Value = PC_ID;
                        command.Parameters.Add("@EDateTime", SqlDbType.VarChar).Value = dataTime;
                        command.Parameters.Add("@TotalStonks", SqlDbType.VarChar).Value = TotalStonks;
                        throw new Exception("Упс....");
                    }
                    else
                    {
                        MessageBox.Show("Аренда успешна, производится выход в главное меню");
                    }
                }
                dataBase.CloseConnection();
            }
            else
            {
                command = new SqlCommand("DELETE FROM Аренда WHERE ID_пользователя = @UserID AND Прибыль = @Stonks AND Время_начала = @FDateTime", dataBase.GetConnection());
                command.Parameters.Add("@UserID", SqlDbType.VarChar).Value = User_ID;
                command.Parameters.Add("@Stonks", SqlDbType.VarChar).Value = Stonks;
                command.Parameters.Add("@FDateTime", SqlDbType.VarChar).Value = FDateTime;
                throw new Exception("Упс.....");
            }
            return new Rent(Rent_ID, User_ID, PC_ID, PC_Lock, TotalStonks, IsPayed, dataTime);
        }
    }
}
